services:
  app:
    image: registry.cn-shanghai.aliyuncs.com/topthink/wiki:latest
    restart: always
    command:
      - app:run
    ports:
      - "8080:80" #端口 可以根据需求修改
    environment:
      PHP_DB_HOST: mysql
      PHP_REDIS_HOST: redis
      PHP_QDRANT_HOST: qdrant
      PHP_LICENSE: "" #授权码 https://doc.topthink.com/@thinkwiki-deploy/apply.html
    volumes:
      - ./data/storage:/opt/htdocs/storage
    depends_on:
      - mysql
      - redis
      - qdrant

  qdrant:
    image: registry.cn-shanghai.aliyuncs.com/topopen/qdrant:latest
    restart: always
    volumes:
      - ./data/qdrant:/qdrant/storage

  redis:
    image: registry.cn-shanghai.aliyuncs.com/topopen/redis:latest
    restart: always
    volumes:
      - ./data/redis:/data

  mysql:
    image: registry.cn-shanghai.aliyuncs.com/topopen/mysql:5.7
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=wiki
    volumes:
      - ./data/mysql:/var/lib/mysql
