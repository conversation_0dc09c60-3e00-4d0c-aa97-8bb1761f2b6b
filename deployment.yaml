apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge
  namespace: topthink
  labels:
    app: knowledge
spec:
  replicas: 2
  selector:
    matchLabels:
      app: knowledge
  template:
    metadata:
      labels:
        app: knowledge
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values: [ x ]
              namespaceSelector: { }
              topologyKey: kubernetes.io/hostname
      initContainers:
        - name: init
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/wiki:IMAGE_TAG
          imagePullPolicy: Always
          args:
            - 'app:init'
          env:
            - name: PHP_DB_TYPE
              value: mysql
            - name: PHP_DB_NAME
              value: knowledge
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_REDIS_DB
              value: "9"
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
          volumeMounts:
            - name: volume-storage
              mountPath: /opt/htdocs/storage/
      containers:
        - name: main
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/wiki:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: PHP_APP_TOKEN
              value: "K^_FrW%5^!BysknK"
            - name: PHP_DB_TYPE
              value: mysql
            - name: PHP_DB_NAME
              value: knowledge
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_APP_URL
              value: https://k.topthink.com
            - name: PHP_APP_HOST
              value: k.topthink.com
            - name: PHP_HASHIDS_SALT
              value: k.topthink.com
            - name: PHP_SSH_PORT
              value: "22"
            - name: PHP_CNAME_HOST
              value: topthink.cloud
            - name: PHP_LFS_DOMAIN
              value: https://lfs.k.topthink.com
            - name: PHP_SPACE_MULTI
              value: 'true'
            - name: PHP_EDITOR_CHANNEL
              value: knowledge
            - name: PHP_EDITOR_TOKEN
              value: EnnIOlloBAYwwRNMHJbXYUdKAJTITFzH
            - name: PHP_CLOUD_ENABLE
              value: 'true'
            - name: PHP_CLOUD_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: knowledge-config
                  key: cloud-client-id
            - name: PHP_CLOUD_CLIENT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: knowledge-config
                  key: cloud-client-secret
            - name: PHP_QDRANT_HOST
              value: qdrant-svc.thirdparty
            - name: PHP_QUEUE_TYPE
              value: redis
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_REDIS_DB
              value: "9"
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
            - name: PHP_TRACING_DRIVER
              value: 'zipkin'
            - name: PHP_ZIPKIN_ENDPOINT
              value: 'http://tracing-analysis-dc-sh-internal.aliyuncs.com/adapt_hvfmcpk6dx@82da6cbc6ee1477_hvfmcpk6dx@53df7ad2afe8301/api/v2/spans'
            - name: aliyun_logs_app
              value: /opt/htdocs/runtime/log/*.log
            - name: aliyun_logs_app_project
              value: topthink
            - name: aliyun_logs_app_logstore
              value: knowledge
            - name: aliyun_logs_nginx
              value: /var/log/supervisor/nginx-error.log
            - name: aliyun_logs_nginx_project
              value: topthink
            - name: aliyun_logs_nginx_logstore
              value: knowledge
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
            limits:
              cpu: 500m
              memory: 1024Mi
          ports:
            - containerPort: 80
              protocol: TCP
          volumeMounts:
            - name: volume-storage
              mountPath: /opt/htdocs/storage/
            - name: volume-log
              mountPath: /opt/htdocs/runtime/log/
            - name: volume-supervisor
              mountPath: /var/log/supervisor/
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
      volumes:
        - name: volume-log
          emptyDir: { }
        - name: volume-supervisor
          emptyDir: { }
        - name: volume-storage
          persistentVolumeClaim:
            claimName: knowledge-storage
