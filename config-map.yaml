apiVersion: v1
kind: ConfigMap
metadata:
  name: common-config
  namespace: topthink
data:
  smtp-host: ""
  smtp-port: ""
  smtp-user: ""
  smtp-pass: ""
  local-redis-host: ""
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: knowledge-config
  namespace: topthink
data:
  cloud-enable: true
  cloud-client-id: ""
  cloud-client-secret: ""
  elastic-host: ""
---
apiVersion: v1
data:
  host: ''
  password: ''
  user: ''
kind: Secret
metadata:
  name: db-config
  namespace: topthink
type: Opaque
