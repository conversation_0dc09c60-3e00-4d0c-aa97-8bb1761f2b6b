{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "topthink/framework": "^8.0.x-dev", "topthink/think-orm": "^3.0.x-dev", "topthink/think-filesystem": "^1.0", "topthink/think-swoole": "4.0.x-dev", "yunwuxin/think-twig": "^3.0.9", "yunwuxin/think-auth": "^3.0.x-dev", "topthink/think-migration": "^3.1.x-dev", "nesbot/carbon": "^2.54", "yunwuxin/think-notification": "^3.0.x-dev", "yunwuxin/think-mail": "^4.0", "firebase/php-jwt": "^6.1", "hashids/hashids": "^4.1", "topthink/gitlib": "dev-master", "topthink/think-queue": "^3.0", "topthinkcloud/client": "dev-master", "gabrielelana/byte-units": "^0.5.0", "symfony/serializer": "^6.0", "yunwuxin/think-chunk-upload": "^1.0", "league/mime-type-detection": "^1.9", "ramsey/uuid": "^4.0", "twig/intl-extra": "~3.9.0", "yunwuxin/think-cron": "^3.0", "topthink/think-api": "^1.0", "phpseclib/phpseclib": "^3.0", "yunwuxin/think-saml": "^1.0", "topthink/think-tracing": "^1.0", "jcchavezs/zipkin-opentracing": "^2.0", "hkulekci/qdrant": "^0.4.0", "topthink/tiktoken": "dev-master", "topthink/think-cors": "^1.0", "topthink/think-ai": "dev-master", "yunwuxin/think-social": "^4.0"}, "require-dev": {"topthink/think-dumper": "^1.0", "topthink/think-ide-helper": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}}, "config": {"preferred-install": "dist", "platform-check": false, "platform": {"ext-swoole": "4.6.0", "ext-fileinfo": "1.0.4", "ext-bcmath": "1", "ext-intl": "1"}, "allow-plugins": {"php-http/discovery": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": [{"type": "vcs", "url": "https://git.topthink.com/topteam/gitlib.git"}, {"type": "vcs", "url": "https://git.topthink.com/topteam/tiktoken.git"}]}