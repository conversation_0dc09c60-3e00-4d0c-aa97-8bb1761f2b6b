FROM registry.cn-shanghai.aliyuncs.com/topthink/base:wiki

#配置git
COPY docker/git-hooks /usr/bin/git-hooks
RUN chmod 755 /usr/bin/git-hooks/*

RUN git config --system core.hooksPath /usr/bin/git-hooks
RUN git config --system uploadarchive.allowunreachable true
RUN git config --system user.email "****************"
RUN git config --system user.name "TopThink"
RUN git config --system lfs.standalonetransferagent lfs-folder
RUN git config --system lfs.customtransfer.lfs-folder.path php
RUN git config --system lfs.customtransfer.lfs-folder.args "/opt/htdocs/think git:lfs"
RUN git config --system lfs.customtransfer.lfs-folder.concurrent false

#设置用户
RUN usermod -l git www-data
RUN groupmod -n git www-data

#配置SSH服务端
COPY docker/ssh/* /etc/ssh/
RUN chmod 600 /etc/ssh/ssh_host_*
RUN chmod 644 /etc/ssh/ssh_host_*.pub
RUN sed -ri 's/^PermitRootLogin\s+.*/PermitRootLogin no/' /etc/ssh/sshd_config
RUN sed -ri 's/^#?PasswordAuthentication\s+.*/PasswordAuthentication no/' /etc/ssh/sshd_config
RUN sed -ri '$a\AuthorizedKeysCommandUser git' /etc/ssh/sshd_config
RUN sed -ri '$a\AuthorizedKeysCommand /sbin/think.sh git:authorized-keys-check %u %k' /etc/ssh/sshd_config

#配置SSH客户端
COPY docker/config /var/www/.ssh/config
RUN chown git:git -R /var/www/.ssh
RUN chmod 600 /var/www/.ssh/config

#配置supervisor
ADD docker/supervisord.conf /etc/supervisor/conf.d/web.conf

#think
COPY docker/think.sh /sbin/think.sh
RUN chmod 755 /sbin/think.sh

COPY docker/entrypoint.sh /sbin/entrypoint.sh
RUN chmod 755 /sbin/entrypoint.sh

#配置nginx
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

#安装代码
COPY . /opt/htdocs
ADD compiler/app.tar.gz /opt/htdocs/

RUN rm -rf /opt/htdocs/docker
RUN rm -rf /opt/htdocs/compiler

RUN chown git:git -R /opt/htdocs/runtime

EXPOSE 80

ENTRYPOINT ["/sbin/entrypoint.sh"]
CMD ["app:start"]
