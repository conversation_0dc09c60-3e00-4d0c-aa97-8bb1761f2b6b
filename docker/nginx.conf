server {
    listen  80;

    error_log /dev/stderr warn;
    access_log /dev/stdout main;
    client_max_body_size 20m;
    client_body_buffer_size 5m;

    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 5;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;

    location = /favicon.ico {
         root /opt/htdocs/asset/frontend/dist/asset;
         try_files $uri =404;
         expires 10d;
    }

    location /uploads/ {
        root /opt/htdocs/storage;
        try_files $uri =404;
    }

    location = /admin {
        rewrite ^ /admin/index.html last;
    }

    location ~ ^/admin/api/ {
        try_files /dev/null @swoole;
    }

    location ~ ^/admin/(.*) {
        root /opt/htdocs/asset/backend/dist;
        try_files /$1 /index.html =404;
    }


    location ^~ /x-lfs/ {
        internal;
        alias /opt/htdocs/storage/lfs/;
    }

    location / {
        root /opt/htdocs/asset/frontend/dist;
        try_files $uri @swoole;
    }

    location @swoole {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_pass_header X-Accel-Buffering;
    }
}
