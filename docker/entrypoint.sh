#!/bin/bash
set -e

#确保目录权限
chown git:git /opt/htdocs/storage

phpenmod swoole_loader

case ${1} in
  app:init)
    su -c "php think migrate:run" git
    ;;
  app:start)
    mkdir -p /var/run/sshd
    export -p | grep PHP_ > /opt/env
    rm -rf /var/run/supervisor.sock
    exec /usr/bin/supervisord -nc /etc/supervisor/supervisord.conf
    ;;
  app:run)
    su -c "php think migrate:run" git
    mkdir -p /var/run/sshd
    export -p | grep PHP_ > /opt/env
    rm -rf /var/run/supervisor.sock
    exec /usr/bin/supervisord -nc /etc/supervisor/supervisord.conf
    ;;
  *)
    exec "$@"
    ;;
esac
