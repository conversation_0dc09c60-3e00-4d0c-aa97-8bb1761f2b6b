FROM registry.cn-shanghai.aliyuncs.com/topthink/php:8.2-swoole-nginx

#安装
RUN \
    sed -i "s/archive.ubuntu.com/mirrors.aliyun.com/g" /etc/apt/sources.list && \
    apt-get update && \
    apt-get -y --no-install-recommends install \
    software-properties-common php8.2-gmp openssh-client openssh-server

#安装git
RUN \
    add-apt-repository ppa:git-core/ppa && \
    apt-get update && \
    apt-get -y --no-install-recommends install \
    git git-lfs

COPY ripgrep_13.0.0_amd64.deb /tmp/
RUN dpkg -i /tmp/ripgrep_13.0.0_amd64.deb
RUN rm /tmp/ripgrep_13.0.0_amd64.deb

RUN apt-get clean && rm -rf /var/cache/apt/* && rm -rf /var/lib/apt/lists/*
