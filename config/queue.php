<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'default'     => env('QUEUE_TYPE', 'database'),
    'connections' => [
        'sync'     => [
            'type' => 'sync',
        ],
        'database' => [
            'type'        => 'database',
            'queue'       => 'default',
            'table'       => 'jobs',
            'connection'  => null,
            'retry_after' => 600,
        ],
        'redis'    => [
            'type'        => 'redis',
            'queue'       => 'default',
            'host'        => env('REDIS_HOST', 'redis'),
            'port'        => 6379,
            'password'    => '',
            'select'      => (int) env('REDIS_DB', 0),
            'timeout'     => 0,
            'persistent'  => false,
            'retry_after' => 60 * 20,
        ],
    ],
    'failed'      => [
        'type'  => 'none',
        'table' => 'failed_jobs',
    ],
];
