<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
return [
    // 指令定义
    'commands' => [
        \app\command\Test::class,
        \app\command\UpdateCdnOptions::class,
        \app\command\git\AuthorizedKeysCheck::class,
        \app\command\git\Shell::class,
        \app\command\git\Lfs::class,
        \app\command\git\Hooks::class,
    ],
];
