<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'default'    => 'smtp', //smtp sendmail
    'from'       => [
        'address' => env('MAIL_FROM_ADDRESS', env('SMTP_USER')),
        'name'    => env('MAIL_FROM_NAME', '知识管理'),
    ],
    'transports' => [
        'smtp'     => [
            'host'       => env('SMTP_HOST', 'mail.example.com'),
            'port'       => env('SMTP_PORT', 25),
            'encryption' => env('SMTP_ENCRYPTION', 'tls'),
            'username'   => env('SMTP_USER', ''),
            'password'   => env('SMTP_PASS', ''),
        ],
        'sendmail' => [
            'command' => '/usr/sbin/sendmail -bs',
        ],
    ],
];
