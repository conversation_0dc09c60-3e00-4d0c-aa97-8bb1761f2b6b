<?php
return [
    'default' => env('TRACING_DRIVER', 'noop'),
    'tracers' => [
        'noop'   => [
            'type' => 'noop',
        ],
        'jaeger' => [
            'type'     => 'jaeger',
            'endpoint' => 'http://localhost:9411/api/traces',
            'async'    => false,
        ],
        'zipkin' => [
            'type'     => 'zipkin',
            'name'     => 'topthink-knowledge',
            'endpoint' => env('ZIPKIN_ENDPOINT'),
            'async'    => true,
        ],
    ],
    'errors'  => true,
    'sql'     => true,
    'redis'   => [
        'host'     => env('REDIS_HOST', 'redis'),
        'database' => (int) env('REDIS_DB', 0),
        'port'     => 6379,
    ],
];
