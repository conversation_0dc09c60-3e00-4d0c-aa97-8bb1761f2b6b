<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

//think-auth 配置文件

use app\model\User;
use yunwuxin\auth\provider\Model;

return [
    'default'          => 'web',
    'guards'           => [
        'web'   => [
            'type'     => 'session',
            'provider' => 'model',
        ],
        'api'   => [
            'type'     => 'request',
            'provider' => 'token',
        ],
        'basic' => [
            'type'     => 'basic',
            'provider' => 'git',
        ],
        'saml'  => [
            'type'     => 'session',
            'provider' => 'saml',
        ],
    ],
    'providers'        => [
        'model' => [
            'type'   => Model::class,
            'model'  => User::class,
            'fields' => [
                'username' => 'email',
            ],
        ],
        'git'   => [
            'type'   => \app\lib\GitUserProvider::class,
            'model'  => User::class,
            'fields' => [
                'username' => 'email',
            ],
        ],
        'token' => [
            'type' => \app\lib\TokenUserProvider::class,
        ],
        'saml'  => [
            'type' => \app\lib\SamlUserProvider::class,
        ],
    ],
    //设为false,则不注册路由
    'route'            => false,
    'policy_namespace' => '\\app\\policy\\',
    'policies'         => [],
];
