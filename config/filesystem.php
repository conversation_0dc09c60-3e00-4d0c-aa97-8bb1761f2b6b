<?php

return [
    // 默认磁盘
    'default' => 'uploads',
    // 磁盘列表
    'disks'   => [
        'uploads'      => [
            // 磁盘类型
            'type'       => 'local',
            // 磁盘路径
            'root'       => root_path() . 'storage/uploads',
            // 磁盘路径对应的外部URL路径
            'url'        => '/uploads',
            // 可见性
            'visibility' => 'public',
        ],
        'lfs'          => [
            // 磁盘类型
            'type'       => 'local',
            // 磁盘路径
            'root'       => root_path() . 'storage/lfs',
            // 磁盘路径对应的外部URL路径
            'url'        => '/lfs',
            // 可见性
            'visibility' => 'public',
        ],
        'repositories' => [
            'type' => 'local',
            'root' => root_path() . 'storage/' . env('REPO_DIR', 'repositories'),
        ],
        'pages'        => [
            'type'            => 'local',
            'root'            => root_path() . 'storage/pages',
            'disable_asserts' => true,
        ],
        'release'      => [
            'type' => 'local',
            'root' => root_path() . 'storage/release',
        ],
        'runtime'      => [
            'type'            => 'local',
            'root'            => runtime_path(),
            'disable_asserts' => true,
        ],
        // 更多的磁盘配置信息
    ],
];
