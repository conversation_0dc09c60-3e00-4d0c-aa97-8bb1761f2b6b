stages:
  - base
  - install
  - build
  - deploy

variables:
  KUBECONFIG: /etc/deploy/config

base:
  stage: base
  image:
    name: registry-vpc.cn-shanghai.aliyuncs.com/topthink/executor:debug
    entrypoint: [""]
  before_script:
    - echo "{\"auths\":{\"${CI_REGISTRY_URL}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"},\"$CI_DEPENDENCY_PROXY_SERVER\":{\"auth\":\"$(printf "%s:%s" ${CI_DEPENDENCY_PROXY_USER} "${CI_DEPENDENCY_PROXY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - sed -i 's/registry.cn-shanghai.aliyuncs.com/registry-vpc.cn-shanghai.aliyuncs.com/g' ${CI_PROJECT_DIR}/docker/base/Dockerfile
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}/docker/base"
      --dockerfile "${CI_PROJECT_DIR}/docker/base/Dockerfile"
      --destination "registry-vpc.cn-shanghai.aliyuncs.com/topthink/base:wiki"
  only:
    changes:
      - docker/base/**/*

js:
  stage: install
  cache:
    key: "$CI_COMMIT_REF_NAME-asset"
    paths:
      - asset/.pnpm-store
      - asset/node_modules/
      - asset/frontend/node_modules/
      - asset/backend/node_modules/
      - asset/frontend/dist/
      - asset/backend/dist/
      - view/base.twig
  image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/node:18.18.2-buster
  before_script:
    - npm config set registry https://registry.npmmirror.com
    - npm install -g pnpm@8
  script:
    - cd asset
    - pnpm config set store-dir .pnpm-store
    - pnpm install
    - pnpm build
  only:
    changes:
      - asset/**/*

php:
  stage: install
  cache:
    key: "$CI_COMMIT_REF_NAME-php"
    paths:
      - vendor/
  image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/composer:latest
  script: composer install --optimize-autoloader --prefer-dist --no-dev
  only:
    changes:
      - composer.json
      - composer.lock

compiler:
  stage: install
  cache:
    key: "$CI_COMMIT_REF_NAME-compiler"
    policy: push
    paths:
      - compiler/app.tar.gz
  script:
    - sed -i "s|PROJECT_DIR|$CI_PROJECT_DIR|g" ./compiler/config.ini
    - sudo swoole-compiler30200 -t code -c ./compiler/config.ini
  tags:
    - compiler
  only:
    changes:
      - app/**/*

build:
  stage: build
  image:
    name: registry-vpc.cn-shanghai.aliyuncs.com/topthink/executor:debug
    entrypoint: [""]
  before_script:
    - echo "{\"auths\":{\"${CI_REGISTRY_URL}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"},\"$CI_DEPENDENCY_PROXY_SERVER\":{\"auth\":\"$(printf "%s:%s" ${CI_DEPENDENCY_PROXY_USER} "${CI_DEPENDENCY_PROXY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - sed -i 's/registry.cn-shanghai.aliyuncs.com/registry-vpc.cn-shanghai.aliyuncs.com/g' ${CI_PROJECT_DIR}/Dockerfile
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "registry-vpc.cn-shanghai.aliyuncs.com/topthink/wiki:latest"
      --destination "registry-vpc.cn-shanghai.aliyuncs.com/topthink/wiki:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}"
  cache:
    - key: "$CI_COMMIT_REF_NAME-php"
      policy: pull
      paths:
        - vendor/
    - key: "$CI_COMMIT_REF_NAME-compiler"
      policy: pull
      paths:
        - compiler/app.tar.gz
    - key: "$CI_COMMIT_REF_NAME-asset"
      policy: pull
      paths:
        - asset/frontend/dist/
        - asset/backend/dist/
        - view/base.twig

deploy:
  image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/helm-kubectl-docker:latest
  stage: deploy
  environment:
    name: production
    url: https://wiki.topthink.com
  script:
    - mkdir -p /etc/deploy
    - echo $CI_KUBE_CONFIG | base64 -d > $KUBECONFIG
    - sed -i "s/IMAGE_TAG/$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA/g" deployment.yaml
    - cat deployment.yaml
    - kubectl apply -f deployment.yaml
