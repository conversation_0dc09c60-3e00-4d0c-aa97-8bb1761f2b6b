{"name": "@topthink/wiki-backend", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "dependencies": {"@topthink/common": "^1.5.0", "dayjs": "^1.11.2", "react-bootstrap": "^2.1.2"}, "devDependencies": {"bootstrap": "^5.3.3", "@topthink/webpack-config-plugin": "^1.0.24", "@types/styled-components": "^5.1.11", "@types/lodash": "^4.14.161", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "babel-plugin-styled-components": "^1.13.1", "typescript": "^4.6.4", "webpack": "^5.85.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0"}}