const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");
const Server              = require("webpack-dev-server");

module.exports = async (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    const port = await Server.getFreePort();

    const publicPath = isServer ? `http://localhost:${port}/` : "/admin/asset/";

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/index.tsx",
        output   : {
            filename     : "app.[contenthash:6].js",
            chunkFilename: "[id].[contenthash:6].js",
            path         : path.resolve(__dirname, "dist/asset"),
            clean        : true,
            publicPath
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : {
                    filename          : "../index.html",
                    template          : "public/index.html",
                    inject            : false,
                    scriptLoading     : "blocking",
                    favicon           : "public/favicon.ico",
                    templateParameters: {
                        isDevelopment
                    }
                },
                react: true
            })
        ],
        externals: {
            "react"           : "React",
            "react-dom"       : "ReactDOM",
            "react-dom/client": "ReactDOM"
        },
        devServer: {
            port,
            hot          : true,
            headers      : {
                "Access-Control-Allow-Origin" : "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
                "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
            },
            allowedHosts : "all",
            client       : {
                webSocketURL: {
                    hostname: "localhost"
                }
            },
            devMiddleware: {
                writeToDisk(filePath) {
                    return /\.html$/.test(filePath);
                }
            }
        }
    };
};
