import { ModalButtonProps, Space, User, useRequest } from '@topthink/common';
import InfoModal from './info-modal';
import logoSrc from '../images/logo.svg';

interface Props extends Omit<ModalButtonProps, 'id'> {
    id?: number;
    user?: User;
}

export default function UserModal({ id, user, ...props }: Props) {

    return <InfoModal
        source={id ? `/user/${id}` : undefined}
        header={'用户信息'}
        data={user}
        {...props}
        renderChildren={({ data }) => {

            const { result = [] } = useRequest<OauthToken[]>(`/user/${data.id}/binds`);

            return <>
                <dl className='row'>
                    <dd className='col-2'>昵称</dd>
                    <dd className='col-10'>{data.name}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>邮箱</dd>
                    <dd className='col-10'>{data.email}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>账号绑定</dd>
                    <dd className='col-10'>
                        <Space>
                            {result.map((r) => {
                                switch (r.channel) {
                                    case 'topthink':
                                        return <a href={`https://console.topthink.com/admin/user/list?id=${r.openid}`} target='_blank'>
                                            <img className='rounded' src={logoSrc} width={20} height={20} />
                                        </a>;
                                    default:
                                        return null;
                                }
                            })}
                        </Space>
                    </dd>
                </dl>
            </>;
        }}
    />;
}

