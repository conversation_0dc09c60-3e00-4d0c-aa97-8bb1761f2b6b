import { Content, Table } from '@topthink/common';

export default function User() {
    return <Content title={'用户'}>
        <Table source={'/user'} columns={[
            {
                title: 'ID',
                dataIndex: 'id',
                width: 60
            },
            {
                title: '头像',
                dataIndex: 'avatar',
                width: 45,
                render({ value }) {
                    return <img className='rounded-circle' width={30} height={30} src={value} />;
                }
            },
            {
                title: '昵称',
                dataIndex: 'name'
            },
            {
                title: '邮箱',
                dataIndex: 'email'
            },
            {
                title: '注册时间',
                dataIndex: 'create_time',
                width: 150
            },
        ]} />

    </Content>;
}
