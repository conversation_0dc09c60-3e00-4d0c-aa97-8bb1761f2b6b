import { Card, Content, Loader, useRequest, useParams } from '@topthink/common';

export default function ApplicationRead() {

    const { id } = useParams();

    const { result } = useRequest(`/application/${id}`);

    if (!result) {
        return <Loader />;
    }

    return <Content
        title={`应用：${result.name}`}
    >
        <Card>
            <table className='table'>
                <tbody>
                <tr>
                    <td>Client Id</td>
                    <td>{result.client_id}</td>
                </tr>
                <tr>
                    <td>Client Secret</td>
                    <td>{result.client_secret}</td>
                </tr>
                <tr>
                    <td>回调URL</td>
                    <td>{result.redirect_uri}</td>
                </tr>
                </tbody>
            </table>
        </Card>
    </Content>;
}
