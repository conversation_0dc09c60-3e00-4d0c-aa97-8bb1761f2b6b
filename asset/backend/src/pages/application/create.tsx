import { Card, Content, Form, useNavigate } from '@topthink/common';

export default function ApplicationCreate() {
    const navigate = useNavigate();
    return <Content title={'创建应用'}>
        <Card>
            <Form
                onSuccess={() => navigate('/application')}
                submitText={'保存'}
                action={`/application`}
                formData={{ is_confidential: true }}
                schema={{
                    type: 'object',
                    properties: {
                        name: {
                            type: 'string',
                            title: '名称'
                        },
                        redirect_uri: {
                            type: 'string',
                            title: 'Redirect URI',
                            description: '每个URI占一行'
                        },
                        is_confidential: {
                            type: 'boolean',
                            title: '私密',
                            description: '应用程序可用于客户端密钥可以保持保密的地方。原生移动应用和单页应用被视为为非保密。'
                        }
                    }
                }}
                uiSchema={{
                    redirect_uri: {
                        'ui:widget': 'textarea',
                        'ui:options': {
                            rows: 3
                        }
                    }
                }}
            />
        </Card>
    </Content>;
}
