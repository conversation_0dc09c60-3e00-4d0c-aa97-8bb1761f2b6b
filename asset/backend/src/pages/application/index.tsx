import { Content, LinkButton, Table, Link } from '@topthink/common';

export default function Application() {
    return <Content
        title={'应用'}
        extra={<LinkButton to={'/application/create'}>创建应用</LinkButton>}
    >
        <Table
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Link to={`/application/${record.id}`}>{record.name}</Link>;
                    }
                },
                {
                    dataIndex: 'update_time',
                    title: '更新时间'
                },
                {
                    key: 'action',
                    title: '操作',
                    align: 'right',
                    render({ record }) {
                        return <Link to={`/application/${record.id}/edit`}>编辑</Link>;
                    }
                }
            ]}
            source={'/application'}
        />
    </Content>;
}
