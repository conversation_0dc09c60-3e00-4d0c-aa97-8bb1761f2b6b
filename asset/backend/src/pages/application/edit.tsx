import { Card, Content, Form, Loader, useRequest, useNavigate, useParams } from '@topthink/common';
import { pick } from 'lodash';

export default function ApplicationEdit() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { result } = useRequest(`/application/${id}`);

    if (!result) {
        return <Loader />;
    }

    return <Content
        title={'编辑应用'}
    >
        <Card>
            <Form
                onSuccess={() => navigate('/application')}
                submitText={'保存'}
                action={`/application/${id}`}
                method={'put'}
                formData={pick(result, ['name', 'redirect_uri', 'is_confidential'])}
                schema={{
                    type: 'object',
                    properties: {
                        name: {
                            type: 'string',
                            title: '名称'
                        },
                        redirect_uri: {
                            type: 'string',
                            title: 'Redirect URI',
                            description: '每个URI占一行'
                        },
                        is_confidential: {
                            type: 'boolean',
                            title: '私密',
                            description: '应用程序可用于客户端密钥可以保持保密的地方。原生移动应用和单页应用被视为为非保密。'
                        }
                    }
                }}
                uiSchema={{
                    redirect_uri: {
                        'ui:widget': 'textarea',
                        'ui:options': {
                            rows: 3
                        }
                    }
                }}
            />
        </Card>
    </Content>;
}
