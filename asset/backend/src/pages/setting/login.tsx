import { Card, Form, Loader, useRequest } from '@topthink/common';

export const Component = () => {

    const { result } = useRequest('/setting/login');

    if (!result) {
        return <Loader />;
    }
    return <Card>
        <Form
            method={'post'}
            action={'/setting/login'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    register: {
                        type: 'boolean',
                        title: '开放注册',
                        description: '关闭后，只允许通过邀请注册。'
                    },
                    qrcode: {
                        type: 'boolean',
                        title: '扫码登录',
                        description: '由顶想云提供的微信扫码登录，关闭时请确认管理员已绑定邮箱并设置密码，否则将无法登录'
                    },
                    email: {
                        type: 'object',
                        title: '邮箱登录',
                        description: '需要先完成"邮箱设置",否则无法接受验证码绑定邮箱',
                        properties: {
                            enable: {
                                title: '开启',
                                type: 'boolean',
                            },
                        },
                    },
                }
            }}
        />
    </Card>;
};
