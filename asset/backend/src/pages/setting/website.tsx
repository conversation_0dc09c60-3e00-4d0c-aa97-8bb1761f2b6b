import { Card, Form, Loader, useRequest } from '@topthink/common';

export const Component = function() {

    const { result } = useRequest('/setting/website');

    if (!result) {
        return <Loader />;
    }

    return <Card>
        <Form
            action={'/setting/website'}
            submitText={'保存'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    header: {
                        type: 'string',
                        title: '头部导航',
                        description: '格式如下（每行一条）:<br />名称|链接|打开方式<br />如：<br /><code>帮助|https://some.com/help|_blank<br />内页|/some</code>'
                    },
                    footer: {
                        type: 'string',
                        title: '脚部信息',
                        description: '支持html'
                    },
                    scripts: {
                        type: 'string',
                        title: '自定义脚本'
                    },
                }
            }}
            uiSchema={{
                header: {
                    'ui:widget': 'textarea',
                    'ui:options': {
                        rows: 3
                    }
                },
                footer: {
                    'ui:widget': 'textarea',
                    'ui:options': {
                        rows: 3
                    }
                },
                scripts: {
                    'ui:widget': 'textarea',
                    'ui:options': {
                        rows: 5
                    }
                }
            }}
        />
    </Card>;
};
