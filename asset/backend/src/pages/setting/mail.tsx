import { Card, Form, Loader, useRequest } from '@topthink/common';

export const Component = () => {

    const { result } = useRequest('/setting/mail');

    if (!result) {
        return <Loader />;
    }

    return <Card>
        <Form
            action={'/setting/mail'}
            submitText={'保存'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    from_address: {
                        type: 'string',
                        title: '发件人'
                    },
                    from_name: {
                        type: 'string',
                        title: '发件人名称'
                    },
                    smtp_host: {
                        type: 'string',
                        title: 'SMTP 主机'
                    },
                    smtp_port: {
                        type: 'number',
                        title: 'SMTP 端口'
                    },
                    smtp_username: {
                        type: 'string',
                        title: 'SMTP 用户名'
                    },
                    smtp_password: {
                        type: 'string',
                        title: 'SMTP 密码'
                    },
                    smtp_encryption: {
                        type: 'string',
                        title: '加密',
                        enum: ['ssl', 'tls', ''],
                        default: 'ssl'
                    }
                },
            }}
            uiSchema={{
                from_address: {
                    'ui:widget': 'email',
                    'ui:placeholder': 'mail.example.com'
                },
                from_name: {
                    'ui:placeholder': '知识管理'
                },
                smtp_host: {
                    'ui:placeholder': 'smtp.example.com'
                },
                smtp_port: {
                    'ui:placeholder': '465'
                },
                smtp_encryption: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        enumNames: ['SSL', 'TLS', '无'],
                    }
                }
            }}
        />
    </Card>;
};
