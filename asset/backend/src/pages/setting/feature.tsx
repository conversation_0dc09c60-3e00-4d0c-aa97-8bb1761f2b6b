import { Card, Form, Loader, useRequest } from '@topthink/common';

export const Component = () => {
    const { result } = useRequest('/setting/feature');

    if (!result) {
        return <Loader />;
    }

    return <Card>
        <Form
            action={'/setting/feature'}
            submitText={'保存'}
            formData={result}
            schema={{
                type: 'object',
                properties: {
                    moderation: {
                        type: 'object',
                        title: '合规审核',
                        properties: {
                            enable: {
                                type: 'boolean',
                                title: '开启文档内容自动审核'
                            }
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            token: {
                                                type: 'string',
                                                title: 'API令牌',
                                                description: '使用的ThinkAPI的 <a class="link-primary" target="_blank" href="https://www.topthink.com/api/138">文本审核</a> 接口'
                                            }
                                        }
                                    },
                                ]
                            }
                        }

                    }
                }
            }}
            uiSchema={{}}
        />
    </Card>;
};
