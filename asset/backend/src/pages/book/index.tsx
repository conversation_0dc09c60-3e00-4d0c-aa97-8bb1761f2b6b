import { Content, LinkButton, RequestButton, Space, Table, Tooltip } from '@topthink/common';
import { Badge } from 'react-bootstrap';
import InfoModal from '@/components/info-modal';

export default function Book() {
    return <Content title={'文档'}>
        <Table
            search
            sync
            source={'/book'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '',
                    dataIndex: 'visibility_level',
                    width: 10,
                    render({ value }) {
                        switch (value) {
                            case 20:
                                return <Tooltip tooltip={'公开'}><i className='bi bi-globe text-secondary' /></Tooltip>;
                            case 10:
                                return <Tooltip tooltip={'内部'}><i
                                    className='bi bi-shield-shaded text-secondary' /></Tooltip>;
                            case 0:
                                return <Tooltip tooltip={'私有'}><i className='bi bi-lock text-secondary' /></Tooltip>;
                        }
                    }
                },
                {
                    title: '',
                    dataIndex: 'logo',
                    width: 30,
                    render({ value }) {
                        return <img className='rounded' src={value} width='30' height='30' />;
                    }
                },
                {
                    title: '名称',
                    dataIndex: 'name',
                    render({ value, record }) {
                        return <Space>
                            <a href={record.url} target={'_blank'}>{value}</a>
                            {record.block_time && <Badge bg={'danger'}>已禁用</Badge>}
                        </Space>;
                    }
                },
                {
                    title: '最近发布',
                    dataIndex: 'release_time',
                    width: 150
                },
                {
                    title: '最近更新',
                    dataIndex: 'update_time',
                    width: 150
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    width: 180,
                    render({ record, action }) {
                        return <Space>
                            <InfoModal
                                source={`/book/${record.id}`}
                                text={'文档信息'}
                                renderChildren={({ data }) => {
                                    return <>
                                        <dl className='row'>
                                            <dd className='col-2'>名称</dd>
                                            <dd className='col-10'>{data.name}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-2'>仓库路径</dd>
                                            <dd className='col-10'>{data.hash_path}</dd>
                                        </dl>
                                    </>;
                                }}
                            />
                            <LinkButton to={`/book/${record.id}/article`}>章节信息</LinkButton>
                            {record.block_time ? <RequestButton
                                url={`/book/${record.id}/unblock`}
                                onSuccess={action.reload}
                                confirm={'确定要恢复吗？'}
                                method={'post'}>恢复</RequestButton> :
                                <RequestButton
                                    url={`/book/${record.id}/block`}
                                    onSuccess={action.reload}
                                    confirm={'确定要禁用吗？'}
                                    method={'post'}>禁用</RequestButton>}
                        </Space>;
                    }
                }
            ]} />
    </Content>;
}
