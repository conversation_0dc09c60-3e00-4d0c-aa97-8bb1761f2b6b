import { Content, RequestButton, Space, Table } from '@topthink/common';
import InfoModal from '@/components/info-modal';
import { useState, Key } from 'react';

export default function Moderation() {
    const [selected, setSelected] = useState<Key[]>([]);
    return <Content>
        <Table
            source={'/book/moderation'}
            toolBarRender={(action) => {
                return <Space>
                    <RequestButton
                        confirm='确认批量禁用吗？'
                        variant='danger'
                        disabled={selected.length === 0}
                        url={{
                            method: 'post',
                            url: `/book/moderation/mark`,
                            data: {
                                ids: selected,
                                operation: 'block'
                            }
                        }}
                        onSuccess={action.reload}
                    >违规并禁用</RequestButton>
                    <RequestButton
                        confirm='确认批量忽略吗？'
                        variant='success'
                        disabled={selected.length === 0}
                        url={{
                            method: 'post',
                            url: `/book/moderation/mark`,
                            data: {
                                ids: selected,
                                operation: 'pass'
                            }
                        }}
                        onSuccess={action.reload}
                    >正常并忽略</RequestButton>
                    <RequestButton
                        confirm='确认批量操作吗？'
                        variant='warning'
                        disabled={selected.length === 0}
                        url={{
                            method: 'post',
                            url: `/book/moderation/mark`,
                            data: {
                                ids: selected,
                                operation: 'review'
                            }
                        }}
                        onSuccess={action.reload}
                    >违规并整改</RequestButton>
                </Space>;
            }}
            rowSelection={{
                selectedRowKeys: selected,
                onChange: (keys) => {
                    setSelected(keys);
                }
            }}
            tableLayout={'fixed'}
            columns={[
                {
                    title: '章节',
                    dataIndex: 'title',
                    render({ record }) {
                        return <InfoModal<PageFile>
                            className={'text-truncate w-100 text-start'}
                            modalProps={{ size: 'xl', footer: false }}
                            source={`/book/${record.book_id}/article/${record.id}/content`}
                            text={record.title}
                            renderChildren={({ data: { content } }) => {
                                return <span className='text-pre-wrap'>{content}</span>;
                            }}
                        />;
                    }
                },
                {
                    title: '违禁词',
                    dataIndex: 'remark',
                    width: 300,
                    className: 'text-truncate',
                },
                {
                    title: '最近更新',
                    dataIndex: 'update_time',
                    width: 150,
                    render({ value }) {
                        return value ? value : '--';
                    }
                },
                {
                    title: '所属文档',
                    dataIndex: 'book',
                    className: 'text-truncate',
                    width: 150,
                    render({ value }) {
                        return <a className='link-primary' href={value.url} target={'_blank'}>{value.name}</a>;
                    }
                },
            ]}
        />
    </Content>;
}
