import { Content, Space, Table, useRouteLoaderData } from '@topthink/common';
import InfoModal from '@/components/info-modal';

export default function Article() {

    const book = useRouteLoaderData('book') as Book;

    return <Content title={`${book.name} - 章节信息`} showBack>
        <Table
            source={`/book/${book.id}/article`}
            columns={[
                {
                    title: '标题',
                    dataIndex: 'title',
                },
                {
                    title: '文件名',
                    dataIndex: 'ref',
                },
                {
                    title: '访问地址',
                    dataIndex: 'path',
                },
                {
                    title: '最近更新',
                    dataIndex: 'update_time',
                    width: 160,
                    render({ value }) {
                        return value ? value : '--';
                    }
                },
                {
                    title: '是否训练',
                    dataIndex: 'train_time',
                    align: 'center',
                    width: 80,
                    render({ record }) {
                        if (record.update_time === null) {
                            return '--';
                        }
                        return record.train_time === record.update_time ? <span className='text-success'>是</span> :
                            <span className='text-danger'>否</span>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render({ record }) {
                        return <Space>
                            <InfoModal<PageFile>
                                modalProps={{ size: 'xl', footer: false }}
                                source={`/book/${book.id}/article/${record.id}/content`}
                                text='章节内容'
                                renderChildren={({ data: { content } }) => {
                                    return <span className='text-pre-wrap'>{content}</span>;
                                }}
                            />
                            <InfoModal<ArticlePart[]>
                                modalProps={{ size: 'xl', footer: false }}
                                source={`/book/${book.id}/article/${record.id}/part`}
                                text='分段信息'
                                renderChildren={({ data }) => {
                                    return <Space direction='vertical'>
                                        {data.map(({ payload }, index) => {
                                            return <fieldset key={index} className='border rounded p-3'>
                                                <legend className='float-none w-auto fs-6 px-2 mb-0'>分段{index + 1}</legend>
                                                <div className='text-pre-wrap'>
                                                    {`${payload.title || ''}\n${payload.content}`.trim()}
                                                </div>
                                            </fieldset>;
                                        })}
                                    </Space>;
                                }}
                            />
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
