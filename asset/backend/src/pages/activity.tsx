import { Card, Content, useRequest, Pagination, Loader } from '@topthink/common';
import { useCallback, useEffect, useState } from 'react';

interface ActivityType {
    id: number;
    user: {
        avatar: string
        name: string
    };
    create_time: string;
    html: string;
}

export default function Activity() {

    const [pagination, setPagination] = useState({ total: 0, current: 1, pageSize: 10 });
    const [data, setData] = useState<ActivityType[]>([]);

    const { result, execute, loading } = useRequest('/activity', {
        refreshDeps: [pagination.current],
    });

    useEffect(() => {
        if (result) {
            setPagination({
                total: result.total,
                current: result.current_page,
                pageSize: result.per_page
            });
            setData(result.data);
        }
    }, [result]);

    const handleChange = useCallback((current: number) => {
        execute({ url: '/activity', params: { page: current } });
    }, [execute]);

    return <Content title={'动态'}>
        <Card>
            <Loader loading={loading} />
            <div className='mb-3'>
                {data.map((activity) => {
                    return <div key={activity.id} className='d-flex border-bottom pt-3'>
                        <img className='bd-placeholder-img flex-shrink-0 me-3 rounded-circle'
                             src={activity.user.avatar}
                             width='32'
                             height='32' />
                        <div className='pb-3 flex-fill'>
                            <p className='mb-1 d-flex justify-content-between'>
                                <strong className='text-gray-dark'>{activity.user.name}</strong>
                                <span className='text-black-50'>{activity.create_time}</span>
                            </p>
                            <div dangerouslySetInnerHTML={{ __html: activity.html }}></div>
                        </div>
                    </div>;
                })}
            </div>
            <Pagination className={'mb-0'} {...pagination} onChange={handleChange} />
        </Card>
    </Content>;
}
