import { Loader, NumberFormat, Statistic, useRequest } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

export default function Basic() {
    const { result } = useRequest<BasicStatistics>('/statistic/basic');

    if (!result) {
        return <Loader />;
    }

    return <Row className={'g-3 mb-3'}>
        <Col>
            <Statistic
                title={'用户'}
                content={
                    <NumberFormat value={result.user.total} currency={false} options={{ minimumFractionDigits: 0 }} />
                }
                footer={
                    <span>昨日新增 {result.user.yesterday}</span>
                }
            />
        </Col>
        <Col>
            <Statistic
                title={'文档'}
                content={
                    <NumberFormat value={result.book.total} currency={false} options={{ minimumFractionDigits: 0 }} />
                }
                footer={
                    <span>昨日新增 {result.book.yesterday}</span>
                }
            />
        </Col>
    </Row>;
}
