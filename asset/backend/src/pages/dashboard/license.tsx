import { Card, formatL<PERSON><PERSON><PERSON>ber, Loader, useRequest } from '@topthink/common';
import { ReactNode } from 'react';
import { Alert } from 'react-bootstrap';
import dayjs from 'dayjs';

export default function License() {
    const { result, error } = useRequest('/license');

    let children: ReactNode = null;

    if (error) {
        children = <Alert variant={'danger'}>未授权</Alert>;
    } else {
        if (result) {
            children = <table>
                <tbody>
                <tr>
                    <td className={'text-muted'}>有效期：</td>
                    <td width={90}>{dayjs(result.expire_time).format('YYYY-MM-DD')}</td>
                    <td>
                        <a className={'link-primary'} href={'https://console.topthink.com/license/doc'} target={'_blank'}>续费</a>
                    </td>
                </tr>
                <tr>
                    <td className={'text-muted'}>Token 余额：</td>
                    <td>{formatLongNumber(result.token)}</td>
                    <td>
                        <a className={'link-primary'} href={'https://console.topthink.com/license/doc'} target={'_blank'}>充值</a>
                    </td>
                </tr>
                </tbody>
            </table>;
        } else {
            children = <Loader wrap={60} />;
        }
    }

    return <Card title={'授权信息'}>
        {children}
    </Card>;
}
