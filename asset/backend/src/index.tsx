import { createRoot } from 'react-dom/client';
import './scss/app.scss';
import { createApplication, request, useLocation } from '@topthink/common';
import routes from '@/routes';
import { useEffect } from 'react';

const container = document.getElementById('app');

if (container) {
    const root = createRoot(container);

    const App = createApplication({
        basename: '/admin',
        baseURL: '/admin/api',
        authentication: 'cookie',
        routes,
        async appResolver() {
            return await request('/manifest');
        },
        async userResolver() {
            return await request('/current');
        },
        onLogin: () => {
            const { state } = useLocation();
            useEffect(() => {
                if (state?.from === 'logout') {
                    window.location.replace('/-/user/logout');
                } else {
                    window.location.replace('/-/user/login');
                }
            }, []);
            return null;
        }
    });

    root.render(<App />);
}
