import { useApp } from '@topthink/common';
import get from 'lodash/get';

function useManifest(): Manifest;
function useManifest<TKey1 extends keyof Manifest>(key1: TKey1): Manifest[TKey1];
function useManifest<TKey1 extends keyof Manifest, <PERSON><PERSON><PERSON><PERSON> extends keyof Manifest[TKey1]>(key1: TKey1, key2: TKey2): Manifest[TKey1][TKey2];
function useManifest<TKey1 extends keyof Manifest, TKey2 extends keyof Manifest[TKey1], TK<PERSON>3 extends keyof Manifest[TKey1][TKey2]>(key1: TKey1, key2: TKey2, key3: TKey3): Manifest[TKey1][TKey2][TKey3];
function useManifest(...path: any[]) {
    const manifest = useApp();
    if (path.length > 0) {
        return get(manifest, path);
    }
    return manifest;
}

export default useManifest;
