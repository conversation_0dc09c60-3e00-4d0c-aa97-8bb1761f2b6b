declare module '*.svg' {
    import { ElementType } from 'react';
    const content: any;
    export default content;
    export const ReactComponent: ElementType;
}

interface Book {
    name: string;
    id: number;
}

interface PageFile {
    content: string;
}

interface ArticlePart {
    id: string;
    payload: {
        title: string;
        content: string;
    };
}

interface OauthToken {
    channel: string;
    openid: string;
}

interface Manifest {
    saas: boolean;
}

interface BasicStatistics {
    user: {
        total: number;
        yesterday: number;
    };
    book: {
        total: number;
        yesterday: number;
    };
}
