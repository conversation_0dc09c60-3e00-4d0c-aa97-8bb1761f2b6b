{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2018"], "jsx": "react-jsx", "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "baseUrl": "./", "esModuleInterop": true, "experimentalDecorators": true, "skipLibCheck": true, "strictNullChecks": true, "resolveJsonModule": true, "paths": {"@/*": ["./src/*"]}}, "include": ["./src/**/*"]}