export default function(url: string) {

    $('[data-bs-selector="change"]').api({
        on: 'change',
        url,
        method: 'PUT',
        data: function() {
            let value;
            if ($(this).val() === 'on') {
                value = $(this).prop('checked') ? 1 : 0;
            } else {
                value = $(this).val();
            }

            return {
                field: $(this).prop('name'),
                value
            };
        }
    });
}
