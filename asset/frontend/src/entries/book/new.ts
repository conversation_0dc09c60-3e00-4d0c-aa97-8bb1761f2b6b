import { request } from '@topthink/core';

function escapeHtml(html: string) {
    return html.replace(/</g, '&lt;').replace(/>/g, '&gt;');
}

export default function(url: string) {
    let state = location.hash.substr(1);

    window.onhashchange = function() {
        state = location.hash.substr(1);
        changeState();
    };

    const $select = $('#select');
    const $content = $('#content');
    const $body = $('#body');

    const $form = $<HTMLFormElement>('form');
    let $extra: JQuery | null = null;

    function resetBody() {
        if ($extra) {
            $extra.remove();
            $extra = null;
        }
        $body.empty();
        $body.append($form);
        $form.form('clearErrors');
    }

    const changeState = () => {
        $form[0].reset();
        if (state) {
            $select.attr('hidden', '');
            $content.removeAttr('hidden');

            resetBody();

            let selector = `[href="#${state}"]`;

            switch (true) {
                case state === 'git':
                    $extra = $(`<div class="mb-3">
                                <label for="import_url" class="form-label">Git仓库URL</label>
                                <input type='hidden' name='original_type' value='git' />
                                <input type="text" name='original_path' class="form-control" placeholder='https://username:<EMAIL>/group/project.git' id="import_url" />
                            </div>`);
                    $extra.prependTo($form);
                    break;
                case state === 'token':
                    $extra = $(`<div class="mb-3">
                                <label for="import_url" class="form-label">文档令牌</label>
                                <input type='hidden' name='original_type' value='token' />
                                <input type="text" name='original_path' class="form-control" placeholder='请填入具有"文档数据(读取)"权限的令牌' id="import_url" />
                            </div>`);
                    $extra.prependTo($form);
                    break;
                case state === 'kancloud':
                    $body.empty();
                    const $token = $(`<div class='mb-3'>
<label class="form-label">API令牌</label>
<div class="input-group">
<input type="text" class="form-control" name='token' placeholder="请输入API令牌"  />
<button class="btn btn-primary" type="button">读取数据</button>
</div>
</div>`);

                    const $container = $('<div />');

                    $token.find('button').api({
                        url: '/-/book/import',
                        method: 'post',
                        data: function() {
                            const token = $body.find('[name=token]').val();
                            return { type: 'kancloud', action: 'getSpaces', payload: { token } };
                        },
                        onSuccess: function({ data }) {
                            $container.empty();
                            const list = data.map((d: any) => `<li class="dropdown-item" data-value="${d.name}">
<img class="rounded" src="${d.owner.avatar}">${escapeHtml(d.owner.name)}
</li>`).join('\n');

                            const $space = $(`<div class="mb-3">
<div class='selection dropdown' data-bs-toggle='selection'>
<div class="dropdown-toggle">
<div class="text default">
选择空间
</div>
</div>
<ul class="dropdown-menu shadow">
${list}
</ul>
</div>
</div>`);

                            const $books = $('<div />');

                            const $selection = $space.find('[data-bs-toggle="selection"]');
                            $selection.selection();

                            $selection.on('change.bs.selection', async function() {
                                const token = $body.find('[name=token]').val();
                                const space = $selection.selection('getValue');
                                try {
                                    $books.html(`<div class="spinner-border text-primary" role="status">
  <span class="visually-hidden">Loading...</span>
</div>`);
                                    const data = await request<any[]>({
                                        url: '/-/book/import',
                                        method: 'post',
                                        data: {
                                            type: 'kancloud', action: 'getBooks', payload: { token, space }
                                        }
                                    });

                                    const keys: string[] = data.map(v => String(v.id));

                                    const selected = new Set;

                                    let checkedAll = false;
                                    let checkedSome = false;

                                    const tbody = data.map((v) => `<tr>
<td><input class="form-check-input" type="checkbox" value='${v.id}'  /></td>
<td width='25'><img src='${v.small_cover}' height='30' /></td><td>${escapeHtml(v.title)}</td></tr>`).join('\n');

                                    const html = `<table class='table align-middle mb-3'>
<thead><tr><td width='30'><input class="form-check-input" type="checkbox" /></td><td colspan='2'>名称</td></tr></thead>
  <tbody>${tbody}</tbody>
</table><button type="submit" class="btn btn-primary">导入文档</button>`;

                                    $books.html(html);

                                    const $allCheck = $books.find('thead [type="checkbox"]');
                                    const $check = $books.find('tbody [type="checkbox"]');
                                    const $submit = $books.find('[type="submit"]');

                                    const updateAllChecked = () => {
                                        checkedAll = keys.every(function(key) {
                                            return selected.has(key);
                                        });
                                        checkedSome = keys.some(function(key) {
                                            return selected.has(key);
                                        });

                                        $allCheck.prop('checked', checkedAll);
                                        $allCheck.prop('indeterminate', !checkedAll && checkedSome);
                                    };

                                    $allCheck.on('change', function() {
                                        if (checkedAll) {
                                            keys.forEach(function(key) {
                                                selected.delete(key);
                                            });
                                            $check.prop('checked', false);
                                        } else {
                                            keys.forEach(function(key) {
                                                selected.add(key);
                                            });
                                            $check.prop('checked', true);
                                        }

                                        updateAllChecked();
                                    });

                                    $check.on('change', function() {
                                        const val = $(this).val();
                                        const checked = $(this).is(':checked');
                                        if (checked) {
                                            selected.add(val);
                                        } else {
                                            selected.delete(val);
                                        }
                                        updateAllChecked();
                                    });

                                    $submit.api({
                                        url: '/-/book/import',
                                        method: 'post',
                                        data: function() {

                                            const books = data.filter(book => selected.has(String(book.id)))
                                                              .map(book => ({
                                                                  name: book.title,
                                                                  path: book.repository_ssh,
                                                                  visibility_level: book.visibility_level
                                                              }));

                                            return {
                                                type: 'kancloud',
                                                action: 'createBooks',
                                                payload: { token, books }
                                            };
                                        }
                                    });

                                    updateAllChecked();

                                } finally {

                                }
                            });

                            $space.appendTo($container);
                            $books.appendTo($container);
                        }
                    });
                    $token.appendTo($body);
                    $container.appendTo($body);

                    break;
                case state.startsWith('template') :
                    selector = '[href="#template"]';

                    const [, id] = state.split('#');

                    $extra = $(`<div class="mb-3">
                                <label for="import_url" class="form-label">模板</label>
                                <input type='hidden' name='original_type' value='template' />
                                <div class='selection dropdown' data-source='${url}' data-bs-toggle='selection'>
                                    <input type="hidden" name="original_path" value="${id || ''}" />
                                    <div class="dropdown-toggle">
                                        <div class="text default">
                                            选择模板
                                        </div>
                                    </div>
                                    <ul class="dropdown-menu shadow"></ul>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>`);

                    $extra.find('[data-bs-toggle="selection"]').selection();
                    $extra.prependTo($form);
                    break;
            }

            const $card = $(selector);

            const title = $card.find('.card-title').text();
            const desc = $card.find('.card-text').text();

            const $title = $content.find('h2');
            const $desc = $title.next('p');

            $title.text(title);
            $desc.text(desc);
        } else {
            $content.attr('hidden', '');
            $select.removeAttr('hidden');
            resetBody();
        }
    };

    changeState();
}
