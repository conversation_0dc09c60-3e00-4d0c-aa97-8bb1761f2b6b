import axios from 'axios';

interface SalesOrder {
    id: number;
    order_no: string;
    user: {
        name: string;
        email: string;
        avatar: string;
    };
    amount: string;
    status: string;
    create_time: string;
}

interface SalesStats {
    total_sales: number;
    total_revenue: string;
    today_sales: number;
    month_sales: number;
}

class PremiumManager {
    private bookUrl: string;

    constructor(bookUrl: string) {
        this.bookUrl = bookUrl;
        this.init();
    }

    private init() {
        this.bindEvents();
        this.loadData();
    }

    private bindEvents() {
        // 付费阅读开关
        const premiumToggle = document.getElementById('premium_enabled') as HTMLInputElement;
        if (premiumToggle) {
            premiumToggle.addEventListener('change', this.handlePremiumToggle.bind(this));
        }

        // 保存设置按钮
        const saveButton = document.getElementById('save-settings');
        if (saveButton) {
            saveButton.addEventListener('click', this.handleSaveSettings.bind(this));
        }

        // 状态筛选
        const statusFilter = document.getElementById('status-filter') as HTMLSelectElement;
        if (statusFilter) {
            statusFilter.addEventListener('change', this.handleFilterChange.bind(this));
        }

        // 日期筛选
        const dateFilter = document.getElementById('date-filter') as HTMLInputElement;
        if (dateFilter) {
            dateFilter.addEventListener('change', this.handleFilterChange.bind(this));
        }

        // 导出记录
        const exportButton = document.getElementById('export-sales');
        if (exportButton) {
            exportButton.addEventListener('click', this.handleExportSales.bind(this));
        }

        // 订单操作
        document.addEventListener('click', this.handleOrderAction.bind(this));
    }

    private handlePremiumToggle(event: Event) {
        const toggle = event.target as HTMLInputElement;
        const priceSettings = document.getElementById('price-settings');
        
        if (priceSettings) {
            if (toggle.checked) {
                priceSettings.classList.remove('d-none');
            } else {
                priceSettings.classList.add('d-none');
            }
        }
    }

    private async handleSaveSettings() {
        const premiumEnabled = (document.getElementById('premium_enabled') as HTMLInputElement)?.checked || false;
        const price = (document.querySelector('input[name="price"]') as HTMLInputElement)?.value || '';
        const freePages = (document.querySelector('input[name="free_pages"]') as HTMLInputElement)?.value || '3';

        if (premiumEnabled && (!price || parseFloat(price) <= 0)) {
            this.showAlert('请设置有效的文档价格', 'warning');
            return;
        }

        try {
            const response = await axios.post(`${this.bookUrl}/premium/settings`, {
                premium_enabled: premiumEnabled,
                price: premiumEnabled ? parseFloat(price) : null,
                free_pages: parseInt(freePages)
            });

            if (response.data.success) {
                this.showAlert('设置保存成功', 'success');
            } else {
                this.showAlert(response.data.message || '保存失败', 'danger');
            }
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showAlert('保存失败，请稍后重试', 'danger');
        }
    }

    private async handleFilterChange() {
        const status = (document.getElementById('status-filter') as HTMLSelectElement)?.value || '';
        const date = (document.getElementById('date-filter') as HTMLInputElement)?.value || '';

        try {
            const params = new URLSearchParams();
            if (status) params.append('status', status);
            if (date) params.append('date', date);

            const response = await axios.get(`${this.bookUrl}/premium/orders?${params.toString()}`);
            this.updateOrdersTable(response.data.orders);
        } catch (error) {
            console.error('筛选订单失败:', error);
            this.showAlert('筛选失败，请稍后重试', 'danger');
        }
    }

    private async handleExportSales() {
        try {
            const response = await axios.get(`${this.bookUrl}/premium/export`, {
                responseType: 'blob'
            });

            const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `销售记录_${new Date().toISOString().split('T')[0]}.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('导出失败:', error);
            this.showAlert('导出失败，请稍后重试', 'danger');
        }
    }

    private async handleOrderAction(event: Event) {
        const target = event.target as HTMLElement;
        const action = target.getAttribute('data-action');
        const orderId = target.getAttribute('data-order-id');

        if (!action || !orderId) return;

        event.preventDefault();

        switch (action) {
            case 'detail':
                await this.showOrderDetail(orderId);
                break;
            case 'refund':
                await this.handleRefund(orderId);
                break;
        }
    }

    private async showOrderDetail(orderId: string) {
        try {
            const response = await axios.get(`${this.bookUrl}/premium/orders/${orderId}`);
            const order = response.data.order;

            // 这里可以显示一个模态框展示订单详情
            // 为了简化，这里只是alert显示
            alert(`订单详情:\n订单号: ${order.order_no}\n用户: ${order.user.name}\n金额: ¥${order.amount}\n状态: ${order.status}`);
        } catch (error) {
            console.error('获取订单详情失败:', error);
            this.showAlert('获取订单详情失败', 'danger');
        }
    }

    private async handleRefund(orderId: string) {
        try {
            const response = await axios.post(`${this.bookUrl}/premium/orders/${orderId}/refund`);
            
            if (response.data.success) {
                this.showAlert('退款申请已提交', 'success');
                // 刷新订单列表
                this.loadData();
            } else {
                this.showAlert(response.data.message || '退款申请失败', 'danger');
            }
        } catch (error) {
            console.error('退款申请失败:', error);
            this.showAlert('退款申请失败，请稍后重试', 'danger');
        }
    }

    private async loadData() {
        try {
            const response = await axios.get(`${this.bookUrl}/premium/data`);
            this.updateStats(response.data.stats);
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    }

    private updateStats(stats: SalesStats) {
        const elements = {
            total_sales: document.querySelector('[data-stat="total_sales"]'),
            total_revenue: document.querySelector('[data-stat="total_revenue"]'),
            today_sales: document.querySelector('[data-stat="today_sales"]'),
            month_sales: document.querySelector('[data-stat="month_sales"]')
        };

        if (elements.total_sales) elements.total_sales.textContent = stats.total_sales.toString();
        if (elements.total_revenue) elements.total_revenue.textContent = `¥${stats.total_revenue}`;
        if (elements.today_sales) elements.today_sales.textContent = stats.today_sales.toString();
        if (elements.month_sales) elements.month_sales.textContent = stats.month_sales.toString();
    }

    private updateOrdersTable(orders: SalesOrder[]) {
        const tbody = document.querySelector('table tbody');
        if (!tbody) return;

        if (orders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-muted text-center py-4">
                        <i class="bi bi-inbox display-6 d-block mb-2"></i>
                        暂无销售记录
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = orders.map(order => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${order.user.avatar}" class="rounded-circle me-2" width="32" height="32" />
                        <div>
                            <div class="fw-medium">${order.user.name}</div>
                            <small class="text-muted">${order.user.email}</small>
                        </div>
                    </div>
                </td>
                <td><code>${order.order_no}</code></td>
                <td><span class="fw-medium">¥${order.amount}</span></td>
                <td>${this.getStatusBadge(order.status)}</td>
                <td>
                    <div>${new Date(order.create_time).toLocaleString()}</div>
                </td>
                <td class="text-end">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            操作
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-order-id="${order.id}" data-action="detail">
                                <i class="bi bi-eye"></i> 查看详情
                            </a></li>
                            ${order.status === 'paid' ? `
                                <li><a class="dropdown-item text-danger" href="#" data-order-id="${order.id}" 
                                       data-action="refund" data-bs-confirm data-message="确定要退款吗？">
                                    <i class="bi bi-arrow-counterclockwise"></i> 申请退款
                                </a></li>
                            ` : ''}
                        </ul>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    private getStatusBadge(status: string): string {
        const badges = {
            'paid': '<span class="badge bg-success">已支付</span>',
            'pending': '<span class="badge bg-warning">待支付</span>',
            'refunded': '<span class="badge bg-secondary">已退款</span>'
        };
        return badges[status] || `<span class="badge bg-danger">${status}</span>`;
    }

    private showAlert(message: string, type: 'success' | 'danger' | 'warning' | 'info' = 'info') {
        // 创建一个简单的alert提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

export default function(bookUrl: string) {
    new PremiumManager(bookUrl);
}
