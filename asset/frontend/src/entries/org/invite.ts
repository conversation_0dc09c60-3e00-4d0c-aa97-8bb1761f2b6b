import { Message, Toast } from '@topthink/core';
import copy from 'copy-to-clipboard';

$('[data-bs-invite]').confirm({
    onSuccess({ data: result }) {
        Message.success({
            title: '邀请链接已创建',
            html: `<p>${result.url}</p><p class='text-muted'>链接有效时长为 72 小时</p>`,
            timer: undefined,
            showConfirmButton: true,
            toast: false,
            position: 'center',
            showCancelButton: true,
            allowOutsideClick: false,
            confirmButtonText: '复制链接',
            cancelButtonText: '关闭',
            preConfirm() {
                copy(result.url);
                Toast.success('邀请链接已复制至剪贴板');
                return false;
            }
        });
    }
});

$('[data-bs-copy]').on('click', function(e) {
    e.preventDefault();
    copy($(this).attr('href') as string);
    Toast.success('邀请链接已复制至剪贴板');
});
