import { request } from '@topthink/core';

$('[data-bs-selector="change"]').api({
    on: 'change',
    url: '/-/org/sso/toggle',
    method: 'POST',
    data: function() {
        return {
            sso: $(this).prop('checked') ? 1 : 0
        };
    }
});

$('[data-bs-idp]').each(function() {
    const $input = $(this).siblings('input[type="file"]') as <PERSON><PERSON><PERSON><PERSON><HTMLInputElement>;

    $input.on('change', async (e) => {
        const file = e.currentTarget.files![0];
        const data = new FormData();
        data.set('file', file);

        $(this).prop('disabled', true);
        try {
            await request({
                url: '/-/org/sso/idp',
                method: 'post',
                data
            });
        } finally {
            $(this).prop('disabled', null);
            e.currentTarget.value = '';
        }
    });

    $(this).on('click', () => {
        $input.trigger('click');
    });
});
