export default function(days: number) {
    const updatePrice = () => {
        const $number = $('[data-bs-tokens-range]');
        const value = parseInt($number.val() as string || '');
        const min = parseInt($number.attr('min') || '');

        const tokens = value - min;
        const price = Math.floor(tokens / 100 * 24 * days / 365);

        $('[name=tokens]').val(tokens);
        $('[data-bs-price]').text(price.toFixed(2));

        if (price > 0) {
            $('[type=submit]').prop('disabled', false);
        } else {
            $('[type=submit]').prop('disabled', true);
        }
    };

    $('[data-bs-tokens-range]').on('change', function() {
        $('[data-bs-tokens-number]').val($(this).val() || 0);
        updatePrice();
    });

    $('[data-bs-tokens-number]').on('change', function() {
        const max = parseInt($(this).attr('max') || '');
        const min = parseInt($(this).attr('min') || '');
        const value = Math.round(($(this).val() as number || 0) / 100) * 100;

        if (value > max) {
            $(this).val(max);
        } else if (value < min) {
            $(this).val(min);
        } else {
            $(this).val(value);
        }

        $('[data-bs-tokens-range]').val(value);
        updatePrice();
    });

}



