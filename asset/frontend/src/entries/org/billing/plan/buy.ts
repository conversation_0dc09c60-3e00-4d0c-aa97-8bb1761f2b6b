import dayjs from 'dayjs';
import $ from 'jquery';

export default function(expireTime: string, price: number, sizePrice: number, tokensPrice: number) {

    $('[data-bs-price-detail]').popover({
        placement: 'top',
        trigger: 'hover',
        html: true,
        sanitize: false,
        content: `<table class='table table-borderless mb-0'><tbody>
<tr><td width='100'>基础费用</td><td><span class='text-primary me-1'>￥${(price).toFixed(2)}</span>/年</td></tr>
${sizePrice > 0 ? `<tr><td width='100'>媒体空间</td><td><span class='text-primary me-1'>￥${(sizePrice).toFixed(2)}</span>/年</td></tr>` : ''}
${tokensPrice > 0 ? `<tr><td width='100'>Tokens额度</td><td><span class='text-primary me-1'>￥${(tokensPrice).toFixed(2)}</span>/年</td></tr>` : ''}
</tbody></table>`
    });

    const today = dayjs();
    const expireDate = dayjs(expireTime);

    const updateDateAndPrice = () => {
        const years = $('[name="years"]:checked').val() as number;

        const date = expireDate.isBefore(today) ? today.add(years, 'year') : expireDate.add(years, 'years');

        $('[data-bs-time]').text(date.format('YYYY年MM月DD日'));

        $('[data-bs-price]').text(((price + sizePrice + tokensPrice) * years).toFixed(2));

    };

    updateDateAndPrice();

    $('[name="years"]').on('change', function() {
        updateDateAndPrice();
    });
}
