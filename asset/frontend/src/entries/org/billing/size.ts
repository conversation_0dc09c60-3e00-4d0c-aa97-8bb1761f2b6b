export default function(days: number) {
    const updatePrice = () => {
        const $number = $('[data-bs-size-number]');
        const value = parseInt($number.val() as string || '');
        const min = parseInt($number.attr('min') || '');

        const size = value - min;
        const price = Math.floor(size * 100 * days / 365);

        $('[name=size]').val(size);
        $('[data-bs-price]').text(price.toFixed(2));

        if (price > 0) {
            $('[type=submit]').prop('disabled', false);
        } else {
            $('[type=submit]').prop('disabled', true);
        }

        $('[data-bs-cdn]').text((value * 10).toFixed(2) + 'GiB');
        $('[data-bs-upload]').text((value * 10).toFixed(2) + 'MiB');
    };

    $('[data-bs-size-range]').on('change', function() {
        $('[data-bs-size-number]').val($(this).val() || 0);
        updatePrice();
    });

    $('[data-bs-size-number]').on('change', function() {
        const max = parseInt($(this).attr('max') || '');
        const min = parseInt($(this).attr('min') || '');
        const value = $(this).val() || 0;
        if (value > max) {
            $(this).val(max);
        } else if (value < min) {
            $(this).val(min);
        }

        $('[data-bs-size-range]').val($(this).val() || 0);
        updatePrice();
    });

}



