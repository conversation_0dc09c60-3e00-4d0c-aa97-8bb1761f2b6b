import { Toast } from '@topthink/core';
import '../../lib/avatar';

export default function(url: string) {

    $('[data-bs-selector="change"]').api({
        on: 'change',
        url,
        method: 'POST',
        data: function() {
            let value;
            if ($(this).val() === 'on') {
                value = $(this).prop('checked') ? 1 : 0;
            } else {
                value = $(this).val();
            }

            return {
                field: $(this).prop('name'),
                value
            };
        }
    });

    const updateIndexBookSelectionVisible = () => {
        const type = $('input[name="index"]:checked').val();
        if (type == 'book') {
            $('#index_book_selection').prop('hidden', false);
        } else {
            $('#index_book_selection').prop('hidden', true);
        }
    };

    $('input[name="index"]').change(function() {
        updateIndexBookSelectionVisible();
    });

    updateIndexBookSelectionVisible();

    $('[data-bs-selector="index"]').each(function() {
        $(this).api({
            url: $(this).data('url'),
            method: 'PUT',
            async onRequest() {
                const type = $('input[name="index"]:checked').val();
                if (type == 'book') {
                    const book = $('input[name="index_book"]').val();
                    if (!book) {
                        Toast.danger('请选择一个文档');
                        return false;
                    }
                }
            },
            data: function() {
                const type = $('input[name="index"]:checked').val();
                switch (type) {
                    case 'default':
                        return { index: null };
                    case 'workspace':
                        return { index: 'workspace' };
                    case 'book':
                        return { index: $('input[name="index_book"]').val() };
                }
            }
        });
    });
}
