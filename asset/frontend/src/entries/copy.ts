import copy from 'copy-to-clipboard';
import { Toast } from '@topthink/core';

$('[data-bs-copy]').each(function() {
    $(this).on('click', function(e) {
        e.preventDefault();

        let val = $(this).data('val');

        if (!val) {
            const $input = $(this).prev('input');
            if ($input.length > 0) {
                val = String($input.val());
            }
        }

        if (val) {
            copy(val);
            const message = $(this).data('message') || '已复制';
            Toast.success(message);
        }
    });
});
