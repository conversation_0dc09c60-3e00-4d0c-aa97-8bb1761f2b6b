import { request } from '@topthink/core';

export default function(from: string, defaultState: string = 'email') {
    if (from) {
        history.replaceState({ from }, '');
    }

    $('[data-bs-login]').api({
        on: history.state?.from === 'logout' ? 'click' : 'now'
    });

    const $switcher = $('[data-bs-switcher]');

    const canSwitch = $switcher.length > 1;

    const $qrcode = $('[data-bs-qrcode]');
    let qrcodeInit = false;

    const checkMiniLogin = (token: string) => {
        request({
            url: '/-/user/login/check',
            params: { token }
        }).catch(() => {
            //过期
            const $refresh = $('<div class="qr-mask"><i class="bi bi-arrow-repeat"></i></div>');
            $refresh.on('click', () => {
                $qrcode.html('<div class="spinner-border text-primary"></div>');
                getMiniQrcode();
            });
            $qrcode.append($refresh);
            $qrcode.next('h5').html('小程序码已过期，请点击刷新');
        });
    };

    const getMiniQrcode = async () => {
        const result = await request('/-/user/login/qrcode');

        $qrcode.html(`<img src="${result.image}" />`);
        $qrcode.next('h5').html('请使用微信，扫码登录');
        checkMiniLogin(result.token);
    };

    if (canSwitch) {
        window.onhashchange = function() {
            changeState();
        };
        $switcher.removeAttr('hidden');
    }

    let state;

    const changeState = () => {
        state = (canSwitch ? location.hash.substring(1) : defaultState) || defaultState;

        $('[data-bs-target]').attr('hidden', '');
        $(`[data-bs-target="${state}"]`).removeAttr('hidden');

        if (state === 'qrcode' && !qrcodeInit) {
            qrcodeInit = true;
            getMiniQrcode();
        }
    };

    changeState();

}
