$('[data-bs-avatar]').each(function() {
    const $img = $(this).find('img');
    const $button = $(this).find('button');
    const $input = $(this).find<HTMLInputElement>('input[type="file"]');

    $input.on('change', (e) => {
        const file = e.currentTarget.files![0];

        const reader = new FileReader();
        reader.onload = () => {
            $img.attr('src', reader.result as string);
        };

        reader.readAsDataURL(file);
    });

    $button.on('click', () => {
        $input.trigger('click');
    });
});

export {};
