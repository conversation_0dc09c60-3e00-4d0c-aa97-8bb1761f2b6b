$('[data-bs-send-code]').each(function() {
    $(this).api({
        data: function() {
            const email = $('[name="email"]').val() as string;
            return {
                email
            };
        },
        onSuccess: function() {
            const $element = $(this);
            requestAnimationFrame(() => {
                let seconds = 60;
                $element.prop('disabled', true);
                $element.text(`${seconds}秒后重发`);

                const countDown = () => {

                    --seconds;

                    if (seconds <= 0) {
                        $element.prop('disabled', null);
                        $element.text('发送验证码');
                    } else {
                        $element.text(`${seconds}秒后重发`);
                        setTimeout(countDown, 1000);
                    }
                };

                setTimeout(countDown, 1000);
            });
        }
    });
});
