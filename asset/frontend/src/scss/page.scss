.page-wrap {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 54px;
  background: #eee;

  header {
    .navbar {
      height: 54px;
    }

    .logo {
      display: flex;
      width: calc(230px - 1.75rem)
    }

    .nav-link {
      .bi {
        font-size: 16px;
      }
    }
  }

  .page-content {
    display: flex;
    flex-direction: row;
    min-height: calc(100vh - 54px);

    & > aside {
      background-color: #f5f5f5;
      border-right: 1px solid #e3e3e3;
      display: flex;
      flex-direction: column;
      height: calc(100vh - 54px);
      position: sticky;
      top: 54px;
      width: 230px;

      .sidebar {
        flex-grow: 1;
      }

    }

    & > main {
      flex: 1;
      display: flex;
      flex-direction: row;

      .page-aside {
        width: 201px;
        background: #FFFFFF;
        height: calc(100vh - 54px);
        position: sticky;
        top: 54px;
        border-right: 1px solid #e3e3e3;
      }

      .page-main {
        flex: 1;
        display: flex;
        flex-direction: column;

        .page-header {
          background-color: #fff;
          display: flex;

          .container {
            padding-right: var(--bs-gutter-x, 0.75rem);
            padding-left: var(--bs-gutter-x, 0.75rem);
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .title {
            font-size: 22px;
            line-height: 64px;
            height: 64px;
          }

          .nav {
            .nav-link {
              position: relative;

              &.active {
                &:after {
                  content: '';
                  height: 2px;
                  background: var(--bs-primary);
                  display: block;
                  position: absolute;
                  left: 1rem;
                  right: 1rem;
                  bottom: -2px;
                }
              }
            }
          }

          .action {
            display: flex;
            flex-wrap: nowrap;
            gap: 5px;
          }
        }

        .page-body {
          margin-top: 24px;
          flex: 1;
        }

        .page-footer {
          text-align: center;
          padding: 16px 0;
          opacity: .8;
        }
      }
    }
  }
}
