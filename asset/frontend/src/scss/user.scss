.user-page {
  padding-top: 45px;

  main {
    .container {
      max-width: 960px;

      .switcher {
        background: var(--bs-primary);
        width: 56px;
        height: 56px;
        clip-path: polygon(100% 0%, 100% 100%, 0% 0%);
        position: absolute;
        right: 0;
        top: 0;
        border-top-right-radius: var(--bs-border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFF;
        cursor: pointer;

        .bi {
          font-size: 1.75rem;
        }
      }

      .qr-image {
        align-items: center;
        border: 1px solid var(--bs-gray-200);
        border-radius: 50%;
        display: flex;
        height: 198px;
        justify-content: center;
        margin: 2rem auto 2rem;
        overflow: hidden;
        padding: 16px;
        position: relative;
        width: 198px;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
    }
  }
}
