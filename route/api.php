<?php

use app\middleware\DomainCheck;
use think\facade\Route;
use yunwuxin\auth\middleware\Authentication;
use yunwuxin\auth\middleware\UseGuard;

Route::group('api', function () {

    Route::get('/', 'index/index');

    Route::group('ai', function () {
        Route::post('chat/completions', 'chat/completions');
    })->prefix('api.ai.');

    Route::group('me', function () {
        Route::get('/', 'index/index');
        Route::get('key/:id', 'key/read');
        Route::post('key', 'key/save');

        Route::post('release', 'index/release');
    })->prefix('api.me.')->middleware(Authentication::class);

    Route::resource('book', 'book.index')->vars(['book' => 'book']);

    Route::group('book/:book', function () {
        Route::get('lfs-files', 'book.index/lfsFiles');
        Route::get('dataset', 'book.index/dataset');
        Route::get('search', 'book.index/search');
        Route::resource('token', 'book.token');
    });

    Route::post('notify/plan/:name', 'notify/plan');
    Route::post('notify/upgrade/:name', 'notify/upgrade');
    Route::post('notify/size', 'notify/size');
    Route::post('notify/tokens', 'notify/tokens');
})
    ->prefix('api.')
    ->middleware(DomainCheck::class)
    ->middleware(UseGuard::class, 'api');
