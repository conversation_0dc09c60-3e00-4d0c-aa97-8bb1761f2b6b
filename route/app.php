<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use app\middleware\AjaxRedirect;
use app\middleware\DomainCheck;
use app\middleware\DomainMode;
use app\Request;
use think\facade\Route;
use think\middleware\SessionInit;
use yunwuxin\auth\middleware\Authentication;

Route::rule('/', fn() => null, 'HEAD');

Route::get('health', function (\think\Cache $cache, \think\Db $db) {
    $db->connect();
    $cache->handler()->ping();
});

Route::group('<book>.git', function () {
    Route::post('git-upload-pack$', 'http/serviceUploadPack');
    Route::post('git-receive-pack$', 'http/serviceReceivePack');
    Route::get('info/refs$', 'http/getInfoRefs');

    Route::group('info/lfs', function () {
        Route::post('objects/batch$', 'lfs/serveBatch');
        Route::put('objects/:oid/:size', 'lfs/upload');
        Route::get('objects/:oid', 'lfs/download');
    });

    Route::get('HEAD$', 'http/getTextFile');
    Route::get('objects/info/alternates$', 'http/getTextFile');
    Route::get('objects/info/http-alternates$', 'http/getTextFile');
    Route::get('objects/info/packs$', 'http/getInfoPacks');
    Route::get('objects/info/:name$', 'http/getTextFile')->pattern(['name' => '[^/]*']);
    Route::get('objects/:name$', 'http/getLooseObject')->pattern(['name' => '[0-9a-f]{2}/[0-9a-f]{38}']);
    Route::get('objects/pack/pack-<name>.pack$', 'http/getPackFile')->pattern(['name' => '[0-9a-f]{40}']);
    Route::get('objects/pack/pack-<name>.idx$', 'http/getIdxFile')->pattern(['name' => '[0-9a-f]{40}']);
})
    ->middleware(DomainCheck::class)
    ->middleware(Authentication::class, 'basic')
    ->prefix('git.');

Route::get('lfs/:oid', 'lfs/read')->pattern(['oid' => '[0-9a-f]{64}']);
//兼容云盾需要一个后缀
Route::get('lfs/<oid>.dat', 'lfs/read')->pattern(['oid' => '[0-9a-f]{64}']);

Route::group(function () {

    Route::group('-', function () {
        Route::redirect('/', '/-/workspace/book');

        Route::group(function () {
            Route::redirect('workspace', '/-/workspace/book');

            Route::group('workspace', function () {
                Route::get('book/[:scope]', 'book/index');

                Route::get('activity', 'activity/index');
                Route::get('recycle', 'recycle/index');
                Route::post('recycle/:id/restore', 'recycle/restore');
            })->prefix('workspace.');

            Route::group('book', function () {
                Route::get('new', 'index/create');
                Route::post('new', 'index/save');
                Route::post('import', 'index/import');
                Route::get('template', 'index/template');

                Route::group(':book', function () {
                    Route::post('quit', 'index/quit');

                    Route::get('dashboard', 'dashboard/index');

                    Route::get('edit', 'edit/index');
                    Route::get('setting', 'index/setting');
                    Route::get('download/:type', 'index/download');
                    Route::get('check', 'index/check');
                    Route::post('retry', 'index/retry');
                    Route::post('slug', 'index/slug');

                    Route::get('member/search', 'member/search');
                    Route::post('member/:id/role', 'member/role')->pattern(['id' => '[\w:]+']);
                    Route::resource('member', 'member')->pattern(['id' => '[\w:]+']);

                    Route::get('premium', 'premium/index');

                    Route::resource('token', 'token');

                    Route::delete('/', 'index/delete');
                    Route::put('/', 'index/update');
                    Route::redirect('/', '/-/book/:book/setting');
                });
            })->prefix('book.');

            //组织管理
            Route::group('org', function () {

                Route::get('upgrade', 'upgrade/index');
                Route::post('upgrade', 'upgrade/save');

                Route::get('new', 'new/index');
                Route::post('new', 'new/save');

                Route::get('setting', 'index/setting');

                Route::resource('member/invite', 'invite');
                Route::post('member/:id/role', 'member/role');
                Route::post('member/:id/team', 'member/team');
                Route::post('member/:id/block', 'member/block');
                Route::post('member/:id/unblock', 'member/unblock');
                Route::delete('member/:id', 'member/delete');

                Route::get('member/search', 'member/search');
                Route::get('member/team/:team', 'member/index');
                Route::get('member', 'member/index');

                Route::post('team/:id/member', 'team/member');
                Route::resource('team', 'team');

                Route::get('billing', 'billing.index/index');
                Route::get('billing/plans', 'billing.index/plans');
                Route::get('billing/plan/:name', 'billing.plan/index');
                Route::post('billing/plan/:name', 'billing.plan/save');

                Route::get('billing/size', 'billing.size/index');
                Route::post('billing/size', 'billing.size/save');

                Route::get('billing/tokens', 'billing.tokens/index');
                Route::post('billing/tokens', 'billing.tokens/save');

                Route::get('domain', 'domain.index/index');
                Route::post('domain', 'domain.index/save');
                Route::delete('domain', 'domain.index/delete');
                Route::post('domain/cdn', 'domain.index/cdn');

                Route::get('domain/https', 'domain.https/index');
                Route::post('domain/https', 'domain.https/save');
                Route::get('domain/https/cert', 'domain.https/cert');

                Route::resource('token', 'token');

                Route::get('sso', 'sso/index');
                Route::post('sso/idp', 'sso/idp');
                Route::get('sso/download', 'sso/download');
                Route::post('sso/toggle', 'sso/toggle');

                Route::get('weapp', 'weapp/index');
                Route::post('weapp', 'weapp/save');
                Route::put('weapp', 'weapp/update');

                Route::post('feature', 'index/feature');
                Route::post('restore', 'index/restore');

                Route::get('book', 'index/book');
                Route::put('/', 'index/update');
                Route::delete('/', 'index/delete');
                Route::redirect('/', '/-/org/setting');
            })->prefix('org.');

            Route::group('user', function () {
                Route::get('logout', 'logout/index');

                Route::group('setting', function () {
                    Route::redirect('/', '/-/user/setting/info');
                    Route::get('info', 'info/index');
                    Route::post('info', 'info/save');

                    Route::get('email', 'email/index');
                    Route::post('email', 'email/save');
                    Route::post('email/code', 'email/code');

                    Route::get('password', 'password/index');
                    Route::post('password', 'password/save');

                    Route::get('ssh', 'ssh/index');
                    Route::post('ssh', 'ssh/save');
                    Route::delete('ssh/:id', 'ssh/delete');
                })->prefix('user.setting.')->middleware(DomainMode::class, ~Request::DOMAIN_PARKED);
            })->prefix('user.');
        })->middleware(Authentication::class);

        Route::group('user', function () {
            Route::post('login', 'login/save');
            Route::get('login', 'login/index');
            Route::get('login/cloud', 'login/cloud');
            Route::get('login/qrcode', 'login/qrcode');
            Route::get('login/check', 'login/check');

            Route::get('register', 'register/index');
            Route::post('register/code', 'register/code');
            Route::post('register', 'register/save');

            Route::get('password', 'password/index');
            Route::post('password', 'password/save');
            Route::get('password/edit', 'password/edit');
            Route::put('password', 'password/update');

            Route::get('invite', 'invite/index');
            Route::post('invite', 'invite/save');
        })->prefix('user.')->middleware(DomainMode::class, ~Request::DOMAIN_PARKED);

        Route::group('saml', function () {
            Route::get('metadata', 'saml/metadata');
            Route::get('login', 'saml/login');
            Route::post('acs', 'saml/acs');
        })->middleware(DomainMode::class, ~Request::DOMAIN_SUB);

        Route::miss(function () {
            abort(404);
        });
    });

    Route::group('oauth', function () {
        Route::get('authorize', 'oauth/authorize')->middleware(Authentication::class);
        Route::post('token', 'oauth/token');
        Route::get('jwk', 'oauth/jwk');
    })->middleware(DomainMode::class, ~Request::DOMAIN_PARKED);
})->middleware([DomainCheck::class, SessionInit::class, AjaxRedirect::class]);

Route::group(function () {
    Route::group(function () {
        Route::get('@<id>', function (\think\Request $request) {

            $id    = $request->route('id');
            $query = $request->query();
            $url   = "/@{$id}/";

            if ($query) {
                $url .= "?{$query}";
            }

            return redirect($url, 301);
        });

        Route::post('@<id>/token', 'read/token');

        Route::post('@<id>/search', 'read/search');
        Route::head('@<id>/search', 'read/search');

        Route::post('@<id>/ask', 'read/ask');
        Route::head('@<id>/ask', 'read/ask');

        Route::get('@<id>/:path', 'read/book');

        Route::post('/search', 'read/search');
        Route::head('/search', 'read/search');

        Route::post('/ask', 'read/ask');
        Route::head('/ask', 'read/ask');

        Route::get('/:path', 'read/index');
    })->pattern(['id' => '[a-z0-9_-]{3,64}', 'path' => '\P{C}*']);
})
    ->middleware([
        DomainCheck::class,
        SessionInit::class,
        AjaxRedirect::class,
    ]);
