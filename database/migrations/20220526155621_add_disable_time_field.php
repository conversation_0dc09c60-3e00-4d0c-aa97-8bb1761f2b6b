<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddDisableTimeField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('space')
            ->addColumn(Column::dateTime('block_time')->setAfter('expire_time')->setNullable())
            ->update();

        $this->table('user')
            ->addColumn(Column::dateTime('block_time')->setAfter('update_time')->setNullable())
            ->update();

        $this->table('book')
            ->addColumn(Column::dateTime('block_time')->setAfter('delete_time')->setNullable())
            ->update();
    }
}
