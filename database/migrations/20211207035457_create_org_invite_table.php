<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateOrgInviteTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('organization_invite')
             ->addColumn(Column::integer('org_id'))
             ->addColumn(Column::integer('user_id'))
             ->addColumn(Column::string('key'))
             ->addColumn(Column::tinyInteger('status')->setDefault(1))
             ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
             ->create();

        $this->table('organization_member')
             ->addColumn(Column::integer('invite_id')->setNullable())
             ->update();
    }
}
