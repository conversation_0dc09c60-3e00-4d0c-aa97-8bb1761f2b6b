<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateOrganizationTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('organization')
            ->addColumn(Column::string('name'))
            ->addColumn(Column::string('logo')->setNullable())
            ->addTimestamps()
            ->create();

        $this->table('organization_member')
            ->setId(false)
            ->addColumn(Column::integer('org_id'))
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::integer('access_level')->setDefault(\app\model\Member::READER))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->addIndex(['org_id', 'user_id'], ['unique' => true])
            ->create();
    }
}
