<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBookSalesTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('book_sales')
            ->addColumn(Column::integer('book_id')->setComment('文档ID'))
            ->addColumn(Column::integer('user_id')->setComment('购买用户ID'))
            ->addColumn(Column::string('order_no', 32)->setComment('订单号'))
            ->addColumn(Column::decimal('amount', 8, 2)->setComment('支付金额'))
            ->addColumn(Column::decimal('space_income', 8, 2)->setComment('空间收入(70%)'))
            ->addColumn(Column::decimal('platform_income', 8, 2)->setComment('平台收入(30%)'))
            ->addColumn(Column::enum('status', ['pending', 'paid', 'refunded', 'cancelled'])->setDefault('pending')->setComment('订单状态'))
            ->addColumn(Column::string('payment_method', 20)->setNullable()->setComment('支付方式'))
            ->addColumn(Column::string('transaction_id', 64)->setNullable()->setComment('第三方交易ID'))
            ->addColumn(Column::timestamp('paid_time')->setNullable()->setComment('支付时间'))
            ->addColumn(Column::timestamp('settled_time')->setNullable()->setComment('结算时间'))
            ->addTimestamps()
            ->addIndex(['book_id'])
            ->addIndex(['user_id'])
            ->addIndex(['order_no'], ['unique' => true])
            ->addIndex(['status'])
            ->addIndex(['settled_time'])
            ->create();
    }
}
