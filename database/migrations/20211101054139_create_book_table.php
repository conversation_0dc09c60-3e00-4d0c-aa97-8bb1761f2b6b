<?php

use think\migration\db\Column;
use think\migration\Migrator;

class CreateBookTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('book')
             ->addColumn(Column::integer('space_id'))
             ->addColumn(Column::string('name'))
             ->addColumn(Column::integer('visibility_level')->setDefault(0))
             ->addColumn(Column::string('original_type')->setNullable())
             ->addColumn(Column::string('original_path')->setNullable())
             ->addColumn(Column::tinyInteger('status')->setDefault(0))
             ->addTimestamps()
             ->addSoftDelete()
             ->create();

        $this->table('book_member')
             ->setId(false)
             ->addColumn(Column::integer('book_id'))
             ->addColumn(Column::unsignedInteger('member_id'))
             ->addColumn(Column::string('member_type'))
             ->addColumn(Column::integer('access_level')->setDefault(\app\model\Member::READER))
             ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
             ->addIndex(['book_id', 'member_id', 'member_type'], ['unique' => true])
             ->create();
    }
}
