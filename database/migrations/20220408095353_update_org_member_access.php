<?php

use think\migration\Migrator;
use think\migration\db\Column;

class UpdateOrgMemberAccess extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->execute('UPDATE `organization_member` SET `access_level` = 60 WHERE `access_level` = 50;');
    }

    public function down()
    {
        $this->execute('UPDATE `organization_member` SET `access_level` = 50 WHERE `access_level` = 60;');
    }
}
