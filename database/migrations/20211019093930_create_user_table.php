<?php

use think\migration\db\Column;
use think\migration\Migrator;

class CreateUserTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('user')
             ->addColumn(Column::string('name')->setNullable())
             ->addColumn(Column::string('avatar')->setNullable())
             ->addColumn(Column::string('email'))
             ->addColumn(Column::string('password')->setNullable())
             ->addColumn(Column::string('access_level')->setDefault('regular'))
             ->addColumn(Column::string('remember_token')->setNullable())
             ->addSoftDelete()
             ->addTimestamps()
             ->create();
    }
}
