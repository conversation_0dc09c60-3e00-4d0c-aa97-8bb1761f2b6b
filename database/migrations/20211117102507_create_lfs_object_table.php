<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateLfsObjectTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('lfs_object')
             ->addColumn(Column::string('oid'))
             ->addColumn(Column::bigInteger('size'))
             ->addTimestamps()
             ->create();

        $this->table('lfs_object_book')
             ->setId(false)
             ->addColumn(Column::integer('lfs_object_id'))
             ->addColumn(Column::integer('book_id'))
             ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
             ->addIndex(['book_id', 'lfs_object_id'], ['unique' => true])
             ->create();
    }
}
