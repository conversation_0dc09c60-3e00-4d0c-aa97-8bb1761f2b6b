<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBookArticleTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('book_article')
            ->addColumn(Column::integer('book_id'))
            ->addColumn(Column::string('title'))
            ->addColumn(Column::string('path'))
            ->addColumn(Column::string('ref'))
            ->addTimestamps()
            ->addColumn(Column::timestamp('train_time')->setNullable())
            ->addIndex(['book_id'])
            ->addIndex(['path'])
            ->addIndex(['ref'])
            ->create();
    }
}
