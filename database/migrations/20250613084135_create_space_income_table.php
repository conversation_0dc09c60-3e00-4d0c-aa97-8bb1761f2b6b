<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSpaceIncomeTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('space_income')
            ->addColumn(Column::integer('space_id')->setComment('空间ID'))
            ->addColumn(Column::integer('book_id')->setComment('文档ID'))
            ->addColumn(Column::integer('sale_id')->setComment('销售记录ID'))
            ->addColumn(Column::decimal('amount', 8, 2)->setComment('收入金额'))
            ->addColumn(Column::enum('status', ['pending', 'settled', 'withdrawn'])->setDefault('pending')->setComment('收入状态'))
            ->addColumn(Column::date('income_date')->setComment('收入日期'))
            ->addColumn(Column::timestamp('settled_time')->setNullable()->setComment('结算时间(T+7)'))
            ->addColumn(Column::timestamp('withdrawn_time')->setNullable()->setComment('提现时间'))
            ->addTimestamps()
            ->addIndex(['space_id'])
            ->addIndex(['book_id'])
            ->addIndex(['sale_id'])
            ->addIndex(['status'])
            ->addIndex(['income_date'])
            ->addIndex(['settled_time'])
            ->create();
    }
}
