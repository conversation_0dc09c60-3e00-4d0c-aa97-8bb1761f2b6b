<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateConfigTable extends Migrator
{
    public function change()
    {
        $this->table('setting')
            ->setId(false)
            ->addColumn(Column::string('name'))
            ->addColumn(Column::text('value')->setNullable())
            ->addIndex(['name'], ['unique' => true])
            ->insert([
                'name'  => 'signup_enabled',
                'value' => 'true',
            ])
            ->create();
    }
}
