<?php

use app\model\Setting;
use think\migration\Migrator;
use think\migration\db\Column;

class UpdateSettingTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $website = Setting::where('name', 'in', ['header', 'footer', 'scripts'])
            ->column('value', 'name');

        $website = array_map(function ($value) {
            if ('false' === $value) {
                return false;
            } elseif ('true' === $value) {
                return true;
            }
            return $value;
        }, $website);

        Setting::write('website', $website);
    }

    public function down()
    {

    }
}
