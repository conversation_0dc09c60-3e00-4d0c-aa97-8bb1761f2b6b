{% macro visibility(level) %}
    {% switch level %}
    {% case constant('app\\model\\Book::LEVEL_PUBLIC') %}
        <span data-bs-toggle="tooltip" title="公开">
            <i class="bi bi-globe text-secondary"></i>
        </span>
    {% case constant('app\\model\\Book::LEVEL_PROTECTED') %}
        <span data-bs-toggle="tooltip" title="内部">
            <i class="bi bi-shield-shaded text-secondary"></i>
        </span>
    {% case constant('app\\model\\Book::LEVEL_PRIVATE') %}
        <span data-bs-toggle="tooltip" title="私有">
            <i class="bi bi-lock text-secondary"></i>
        </span>
    {% endswitch %}
{% endmacro %}

{% macro download(book,type,text) %}
    {% set disabled = type not in book.available_pack_types %}
    {% set url = "/-/book/#{book.hash_id}/download/#{type}" %}
    {% if disabled %}
        <li>
            <a class="dropdown-item text-muted d-flex justify-content-between"
               data-tas-doc='maximum'
               href="https://doc.topthink.com/knowledge/download.html" target='_blank'
            >{{ text }}<i class="bi bi-question-circle-fill"></i></a>
        </li>
    {% else %}
        <li>
            <a class="dropdown-item" href="{{ url }}" target='_blank' download>{{ text }}</a>
        </li>
    {% endif %}
{% endmacro %}
