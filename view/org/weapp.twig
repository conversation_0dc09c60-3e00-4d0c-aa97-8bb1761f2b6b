{% extends "org/layout.twig" %}
{% block page_title %}
    小程序阅读
{% endblock %}
{% block page_body %}
    {% if space.isEnterprisePlan(true) %}
        {% if org.weapp_id %}
            {% if weapp is defined %}
                {% if weapp.status == 0 or weapp.status == 2 %}
                    {% embed "./components/result.twig" only %}
                        {% block icon %}
                            <i class="bi bi-info-circle-fill text-info"></i>
                        {% endblock %}
                        {% block message %}
                            等待企业法人认证完成注册
                        {% endblock %}
                    {% endembed %}
                {% elseif weapp.status == 3 %}
                    {% embed "components/card.twig" %}
                        {% block title %}
                            <h5>完善资料</h5>
                        {% endblock %}
                        {% block body %}
                            <form method='put' action='/-/org/weapp' enctype='multipart/form-data' data-bs-form>
                                <div class='alert alert-danger' hidden></div>
                                <div class='mb-3'>
                                    <label class="form-label">小程序名称</label>
                                    <input type="text" name='name' value='' class="form-control"
                                           placeholder='小程序名称' />
                                </div>
                                <div class='mb-3'>
                                    <label class="form-label">小程序介绍</label>
                                    <textarea rows='4' class="form-control" name='signature'
                                              placeholder='小程序介绍'></textarea>
                                </div>
                                <div class='mb-3'>
                                    <label for="name" class="form-label">小程序头像</label>
                                    <div class='d-flex flex-row' data-bs-avatar>
                                        <img class='border rounded' width='56' height='56'
                                             src='{{ asset('images/logo.png') }}' />
                                        <div class='ms-3'>
                                            <button type='button' class='btn btn-outline-secondary btn-sm'>选择图片
                                            </button>
                                            <input name='avatar' hidden type='file' accept="image/jpg,image/png" />
                                            <p class='mb-0'>
                                                <small class='text-muted'>支持文件格式：.png, .jpg</small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">确定</button>
                            </form>
                        {% endblock %}
                    {% endembed %}
                {% elseif weapp.status == 1 %}
                    {% if weapp.version is empty %}
                        {% embed "./components/result.twig" only %}
                            {% block icon %}
                                <i class="bi bi-info-circle-fill text-info"></i>
                            {% endblock %}
                            {% block message %}
                                小程序发布审核中，请稍后
                            {% endblock %}
                        {% endembed %}
                    {% else %}
                        {% if qrcode is defined %}
                            {% embed "./components/result.twig" %}
                                {% block message %}
                                    <img width='200' height='200' src='{{ qrcode }}' />
                                    <p class='mt-4'>v{{ weapp.version }}</p>
                                {% endblock %}
                            {% endembed %}
                        {% else %}
                            {% embed "./components/result.twig" only %}
                                {% block icon %}
                                    <i class="bi bi-exclamation-circle-fill text-danger"></i>
                                {% endblock %}
                                {% block message %}
                                    小程序码获取失败，请重试
                                {% endblock %}
                            {% endembed %}
                        {% endif %}
                    {% endif %}
                {% endif %}
            {% else %}
                {% embed "./components/result.twig" only %}
                    {% block icon %}
                        <i class="bi bi-exclamation-circle-fill text-danger"></i>
                    {% endblock %}
                    {% block message %}
                        未知错误，请联系管理员
                    {% endblock %}
                {% endembed %}
            {% endif %}
        {% else %}
            {% embed "components/card.twig" %}
                {% block title %}
                    <h5>创建微信小程序</h5>
                {% endblock %}
                {% block body %}
                    <form method='post' action='/-/org/weapp' data-bs-form>
                        <div class='alert alert-danger' hidden></div>
                        <div class='mb-3'>
                            <label class="form-label">企业名称</label>
                            <input type="text" name='name' value='' class="form-control" placeholder='企业名称' />
                        </div>
                        <div class='mb-3'>
                            <label class="form-label">企业代码类型</label>
                            <select name='code_type' class='form-control'>
                                <option value='1'>统一社会信用代码（18 位）</option>
                                <option value='2'>组织机构代码（9 位 xxxxxxxx-x）</option>
                                <option value='3'>营业执照注册号(15 位)</option>
                            </select>
                        </div>
                        <div class='mb-3'>
                            <label class="form-label">企业代码</label>
                            <input type="text" name='code' value='' class="form-control" placeholder='企业代码' />
                        </div>
                        <div class='mb-3'>
                            <label class="form-label">法人微信号</label>
                            <input type="text" name='legal_persona_wechat' value='' class="form-control"
                                   placeholder='法人微信号' />
                        </div>
                        <div class='mb-3'>
                            <label class="form-label">法人姓名</label>
                            <input type="text" name='legal_persona_name' value='' class="form-control"
                                   placeholder='法人姓名' />
                        </div>
                        <button type="submit" class="btn btn-primary">开始创建</button>
                    </form>
                {% endblock %}
            {% endembed %}
        {% endif %}
    {% else %}
        {% embed "./components/result.twig" only %}
            {% block icon %}
                <i class="bi bi-exclamation-circle-fill text-danger"></i>
            {% endblock %}
            {% block message %}
                此功能仅企业版可用
                <a class="badge bg-primary link-light" href='/-/org/billing'>升级</a>
            {% endblock %}
        {% endembed %}
    {% endif %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("avatar");
    </script>
{% endblock %}
