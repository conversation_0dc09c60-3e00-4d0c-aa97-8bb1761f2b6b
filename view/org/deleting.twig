{% extends "layout.twig" %}
{% block page_title %}
    空间进入删除流程
{% endblock %}
{% block main %}
    <div class='container page-body'>
        <div class="row justify-content-center h-75 align-items-center">
            <div class="col col-5">
                {% embed 'components/card.twig' %}
                    {% block body %}
                        <div class="d-flex justify-content-center py-4 mb-3">
                            <img src="{{ space.logo }}" class="rounded" width='100' height='100' />
                        </div>
                        <h5 class="card-title mb-4">"{{ space.name }}"空间进入删除流程</h5>
                        <p class="card-text text-muted">删除操作将在 {{ space.delete_time.addDay() }} 生效</p>
                        {% if can(user, 'owner', space) %}
                            <p class="card-text text-muted">在生效前你可以撤销该操作</p>
                            <div class='d-grid'>
                                <a href='/-/org/restore' data-method='post' data-message='确定撤销删除吗？'
                                   data-bs-confirm
                                   class="btn btn-primary">撤销删除</a>
                            </div>
                        {% endif %}
                    {% endblock %}
                {% endembed %}
            </div>
        </div>
    </div>
{% endblock %}
{% block scripts %}

{% endblock %}

