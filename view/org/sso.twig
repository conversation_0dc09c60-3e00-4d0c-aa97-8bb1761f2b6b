{% extends "org/layout.twig" %}
{% block page_title %}
    SSO管理
{% endblock %}
{% block page_content %}
    <div class='alert alert-info'>
        <ul class='mb-0'>
            <li>知识管理支持基于 SAML 2.0 的 SSO（Single Sign On，单点登录），也称为身份联合登录。</li>
            <li><strong>目前单点登录仅适用于通过绑定域名访问私有或者内部文档</strong></li>
        </ul>
    </div>
    <dl class='row'>
        <dt class='col-2'>功能状态</dt>
        <dd class='col-10'>
            <div class="form-check form-switch">
                <input data-bs-selector='change' type="checkbox" class="form-check-input"
                    {{ org.sso?'checked' }}
                    {{ not space.isEnterprisePlan() ?'disabled' }} />
                {% if not space.isEnterprisePlan() %}
                    <a class="badge bg-danger link-light" data-bs-toggle="tooltip" data-bs-placement="bottom"
                       title="此功能仅企业版可用"
                       href='/-/org/billing'>升级</a>
                {% endif %}
            </div>
        </dd>
    </dl>
    <dl class='row'>
        <dt class='col-2'>IdP元数据</dt>
        <dd class='col-10 d-flex align-items-center gap-3'>
            {% if org.idp %}
                <span>
                    已上传
                    <a class='link-primary' target='_blank' href='/-/org/sso/download'>下载</a>
                </span>
            {% endif %}
            <button data-bs-idp type='button' class='btn btn-outline-primary btn-sm'>上传</button>
            <input hidden type='file' accept="application/xml" />
        </dd>
    </dl>
    <dl class='row mb-0'>
        <dt class='col-2'>SP元数据</dt>
        <dd class='col-10 d-flex align-items-center gap-2'>
            <code>{{ space_url(space,'/-/saml/metadata',true) }}</code>
            <a data-bs-copy data-val='{{ space_url(space,'/-/saml/metadata',true) }}' href='#' class='link-primary'>
                <i class="bi bi-clipboard"></i>
            </a>
        </dd>
    </dl>
    {% if is_saas() and not space.parked_domain %}
        <div class='dimmer active rounded'>
            <div class='content'>
                <h3 class='mb-4'>SSO单点登录功能需要在绑定域名下使用</h3>
                <p><a class='btn btn-primary' href='/-/org/domain'>去绑定</a></p>
            </div>
        </div>
    {% endif %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("copy");
        window.import("org/sso");
    </script>
{% endblock %}
