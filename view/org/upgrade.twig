{% extends "layout.twig" %}
{% block web_title %}
    升级空间
{% endblock %}
{% block page_title %}
    升级空间<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/upgrade.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a>
{% endblock %}
{% block main %}
    <div class='container page-body'>
        <div class="row justify-content-center">
            {% embed "components/card.twig" %}
                {% block body %}
                    <div class="row">
                        <div class="col-4">
                            <div class='p-3'>
                                <h2 class='mb-4'>升级为组织空间<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/price.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a></h2>
                                <p>此操作会将你的个人版升级成组织空间，并开通企业版空间15天试用，到期后可以选择购买团队版或企业版空间，查看<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/price.html' target='_blank'>空间版本区别</a></p>
                                <p>每个空间可以独立管理成员、文档和绑定域名</p>
                                <p><strong>此操作不可撤销</strong></p>
                            </div>
                        </div>
                        <div class="col-8">
                            <div class='card-body'>
                                <form action='/-/org/upgrade' data-bs-form method='post'>
                                    <div class='alert alert-danger' hidden></div>
                                    <div class="mb-3">
                                        <label class="form-label">空间名称</label>
                                        <input type="text" name='name' class="form-control"
                                               placeholder='空间名称' />
                                    </div>
                                    <div class='mb-3'>
                                        <label for="name" class="form-label">空间Logo</label>
                                        <div class='d-flex flex-row' data-bs-avatar>
                                            <img class='border rounded' width='56' height='56'
                                                 src='{{ asset('images/logo.svg') }}' />
                                            <div class='ms-3'>
                                                <button type='button' class='btn btn-secondary btn-sm'>选择图片
                                                </button>
                                                <input name='logo' hidden type='file' accept="image/*" />
                                                <p class='mb-0'>
                                                    <small class='text-muted'>支持文件格式：.png, .jpg...</small>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">确认升级</button>
                                </form>
                            </div>
                        </div>
                    </div>
                {% endblock %}
            {% endembed %}
        </div>
    </div>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/create");
    </script>
{% endblock %}
