{% extends "org/layout.twig" %}
{% block page_title %}
    通用设置
{% endblock %}
{% block page_body %}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>基本信息</h5>
            <hr />
            <form method='put' action='/-/org' enctype='multipart/form-data' data-bs-form>
                <div class='alert alert-danger' hidden></div>
                <div class='mb-3'>
                    <label class="form-label">空间名称</label>
                    <input type="text" name='name' value='{{ org.name }}' class="form-control" placeholder='空间名称' />
                </div>
                <div class='mb-3'>
                    <label for="name" class="form-label">空间Logo</label>
                    <div class='d-flex flex-row' data-bs-avatar>
                        <img class='border rounded' width='56' height='56' src='{{ org.logo }}' />
                        <div class='ms-3'>
                            <button type='button' class='btn btn-outline-secondary btn-sm'>选择图片</button>
                            <input name='logo' hidden type='file' accept="image/*" />
                            <p class='mb-0'>
                                <small class='text-muted'>支持文件格式：.png, .jpg...</small>
                            </p>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">保存修改</button>
            </form>
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>功能设置</h5>
            <hr />
            <div class="form-check form-switch ">
                <input data-bs-selector='change' class="form-check-input" type="checkbox"
                       name='powered_by' {{ not org.powered_by?'checked' }}
                       id="powered_by"
                    {{ not space.isEnterprisePlan() ?'disabled' }}
                />
                <label class="form-check-label" for="powered_by">去除构建申明</label>
                {% if not space.isEnterprisePlan() %}
                    <a class="badge bg-danger link-light" data-bs-toggle="tooltip" data-bs-placement="bottom"
                       title="此功能仅企业版可用"
                       href='/-/org/billing'>升级</a>
                {% endif %}
                <div class="form-text">
                    空间下的文档内不再显示“本文档由...构建”字样
                </div>
            </div>
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>空间主页<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/home.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a></h5>
            <hr />
            <p class='text-secondary'>
                设置访问空间域名首页显示的内容
            </p>
            <p class='text-secondary'>
                系统默认提供一个公开文档列表作为空间主页，可设置成跳转到工作台或指定一个文档显示
            </p>
            <p class='text-secondary'>
                <strong>
                    当前：
                    {% if org.index is instance of("\\app\\model\\Book") %}
                        显示指定文档《{{ org.index.name }}》
                    {% elseif org.index == 'workspace' %}
                        跳转至工作台
                    {% else %}
                        系统默认主页
                    {% endif %}
                </strong>
            </p>
            <button data-bs-toggle="modal" data-bs-target="#default-book-modal"
                    class="btn btn-outline-primary">修改空间主页
            </button>
            <div id='default-book-modal' class="modal fade" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">修改空间主页</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="btn-group mb-3">
                                <input type="radio" class="btn-check" name="index" id="index_default" value='default'
                                    {{ not org.index ?'checked' }} />
                                <label class="btn btn-outline-primary" for="index_default">系统默认主页</label>
                                <input type="radio" class="btn-check" name="index" id="index_workspace"
                                       value='workspace' {{ org.index=='workspace' ?'checked' }} />
                                <label class="btn btn-outline-primary" for="index_workspace">跳转工作台</label>
                                <input type="radio" class="btn-check" name="index" id="index_book"
                                       value='book' {{ org.index is instance of("\\app\\model\\Book") ?'checked' }} />
                                <label class="btn btn-outline-primary" for="index_book">指定文档</label>
                            </div>
                            <div id='index_book_selection' class="mb-3">
                                <div data-bs-toggle='selection'
                                     data-search
                                     data-clearable
                                     data-source='/-/org/book'
                                     class='selection dropdown'>
                                    {% if org.index is instance of("\\app\\model\\Book") %}
                                        <input name="index_book" value="{{ org.index.id }}" type="hidden" />
                                    {% else %}
                                        <input name="index_book" value="" type="hidden" />
                                    {% endif %}
                                    <div class="dropdown-toggle">
                                        <div class="text default">输入文档名</div>
                                    </div>
                                    <ul class="dropdown-menu shadow" data-empty='暂无更多数据'></ul>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                取消
                            </button>
                            <button data-url='/-/org' data-bs-selector="index" type="submit" class="btn btn-primary">
                                保存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}
    {% if can(user, 'owner', space) and is_saas() %}
        {% embed "components/card.twig" %}
            {% block body %}
                <h5>删除空间<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/space.html#空间删除' target='_blank'><i class="bi bi-question-circle-fill"></i></a></h5>
                <hr />
                <p class='text-muted'>点击删除，确认后将正式进入删除流程。在 24
                    小时内可以对该操作进行撤销，空间删除过后内容将丢失，请谨慎操作。</p>
                <a data-bs-confirm
                   data-message='<div class="text-start"><p class="fw-bold">确认删除后经正式进入删除流程，在24小时内可对操作进行撤销，删除空间后将视为主动放弃以下资产和权益：</p><ol><li>空间信息、付费权益将被清空且无法恢复</li><li>空间下所有文档、成员等信息将被删除且无法恢复</li></ol></div>'
                   href='/-/org' data-method='delete'
                   class='btn btn-outline-danger'>删除</a>
            {% endblock %}
        {% endembed %}
    {% endif %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/info", "/-/org/feature");
    </script>
{% endblock %}
