{% extends "org/layout.twig" %}
{% block page_title %}
    付费管理
{% endblock %}
{% block page_body %}
    {% embed "components/card.twig" %}
        {% block header %}
            <div class='d-flex justify-content-between align-items-center'>
                当前版本
                {% set actions = space.current_plan.actions(true) %}
                {% if actions %}
                    <div>
                        {{ actions.team|raw }}
                        {{ actions.enterprise|raw }}
                    </div>
                {% endif %}
            </div>
        {% endblock %}
        {% block title %}
            <div class='d-flex justify-content-between align-items-center'>
                {{ space.current_plan.title }}
                <a class='btn btn-outline-secondary btn-sm' href='/-/org/billing/plans'>版本对比</a>
            </div>
        {% endblock %}
        {% block body %}
            <ul class='list-unstyled mb-0'>
                {% for feature in space.current_plan.features %}
                    <li><i class="bi bi-check2 text-success me-2"></i>{{ feature }}</li>
                {% endfor %}
            </ul>
        {% endblock %}
        {% block footer %}
            {{ space.expire_message|raw }}
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block header %}
            <div class='d-flex justify-content-between align-items-center'>
                媒体空间
                {% if space.plan != 'trial' and not space.is_expired %}
                    <a href='/-/org/billing/size' class='btn btn-primary btn-sm'>扩充空间</a>
                {% endif %}
            </div>
        {% endblock %}
        {% block body %}
            <ul class='list-group list-group-flush mb-0'>
                <li class='list-group-item py-3'>
                    <div class='mb-2'>
                        空间大小：{{ format_bytes(space.media_size) }} / {{ format_bytes(space.media_max_size) }}
                    </div>
                    <div class="progress">
                        <div class="progress-bar"
                             style='width:{{ space.media_size/space.media_max_size*100 }}%'></div>
                    </div>
                </li>
                <li class='list-group-item py-3'>
                    <div class='mb-2'>
                        CDN流量(每月)：{{ format_bytes(space.cdn_size) }} / {{ format_bytes(space.cdn_max_size) }}
                    </div>
                    <div class="progress">
                        <div class="progress-bar"
                             style='width:{{ space.cdn_size/space.cdn_max_size*100 }}%'></div>
                    </div>
                </li>
                <li class='list-group-item py-3'>
                    单文件上传大小：{{ format_bytes(space.upload_max_size,'MiB') }}
                </li>
            </ul>
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block header %}
            <div class='d-flex justify-content-between align-items-center'>
                启智AI
                {% if space.plan != 'trial' and not space.is_expired %}
                    <a href='/-/org/billing/tokens' class='btn btn-primary btn-sm'>提升额度</a>
                {% endif %}
            </div>
        {% endblock %}
        {% block body %}
            <ul class='list-group list-group-flush mb-0'>
                <li class='list-group-item py-3'>
                    <div class='mb-2'>
                        Tokens余额(每月)：{{ format_tokens(space.used_ai_tokens) }} / {{ format_tokens(space.max_ai_tokens) }}
                    </div>
                    <div class="progress">
                        <div class="progress-bar"
                             style='width:{{ space.used_ai_tokens/space.max_ai_tokens*100 }}%'></div>
                    </div>
                </li>
            </ul>
        {% endblock %}
    {% endembed %}
{% endblock %}
