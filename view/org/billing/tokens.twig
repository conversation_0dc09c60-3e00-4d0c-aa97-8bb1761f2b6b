{% extends "org/billing/layout.twig" %}
{% block page_title %}
    提升额度
{% endblock %}
{% block form_body %}
    {% embed "components/card.twig" %}
        {% block body %}
            <dl class='row'>
                <dt class='col-2'>Tokens额度(每月)</dt>
                <dd class='col-10'>
                    <div class='row'>
                        <div class='col-7 d-flex align-items-center'>
                            <input
                                data-bs-tokens-range
                                type="range"
                                class="form-range"
                                min="{{ space.max_ai_tokens/1000 }}"
                                max="{{ space.max_ai_tokens/1000+10000 }}"
                                step="100"
                                value='{{ space.max_ai_tokens/1000 }}'
                            />
                        </div>
                        <div class='col-2'>
                            <div class="input-group">
                                <input
                                    data-bs-tokens-number
                                    type="number"
                                    class="form-control"
                                    min="{{ space.max_ai_tokens/1000 }}"
                                    max="{{ space.max_ai_tokens/1000+10000 }}"
                                    step="100"
                                    value='{{ space.max_ai_tokens/1000 }}'
                                />
                                <span class="input-group-text">K</span>
                            </div>
                        </div>
                    </div>
                </dd>
            </dl>
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block body %}
            <div class='d-flex align-items-center justify-content-end'>
                <div class='me-4'>
                    费用：<span class='text-primary'>￥<span data-bs-price class='fs-2'>0.00</span></span>
                </div>
                <div>
                    <button type='submit' class='btn btn-primary px-3' disabled>去支付</button>
                </div>
            </div>
        {% endblock %}
    {% endembed %}
    <input name='tokens' type='hidden' value='0' />
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/billing/tokens",{{ days }});
    </script>
{% endblock %}
