{% extends "org/billing/layout.twig" %}
{% block page_title %}
    升级空间
{% endblock %}
{% block form_body %}
    {% embed "components/card.twig" %}
        {% block body %}
            <dl class='row'>
                <dt class='col-2'>空间大小</dt>
                <dd class='col-10'>
                    <div class='row'>
                        <div class='col-7 d-flex align-items-center'>
                            <input
                                data-bs-size-range
                                type="range"
                                class="form-range"
                                min="{{ space.max_size }}"
                                max="{{ space.max_size+100 }}"
                                step="1"
                                value='{{ space.max_size }}'
                            />
                        </div>
                        <div class='col-2'>
                            <div class="input-group">
                                <input
                                    data-bs-size-number
                                    type="number"
                                    class="form-control"
                                    min="{{ space.max_size }}"
                                    max="{{ space.max_size+100 }}"
                                    step="1"
                                    value='{{ space.max_size }}'
                                />
                                <span class="input-group-text">GiB</span>
                            </div>
                        </div>
                    </div>
                </dd>
            </dl>
            <dl class='row'>
                <dt class='col-2'>CDN流量(每月)</dt>
                <dd class='col-10' data-bs-cdn>
                    {{ format_bytes(space.cdn_max_size) }}
                </dd>
            </dl>
            <dl class='row mb-0'>
                <dt class='col-2'>单文件上传大小</dt>
                <dd class='col-10' data-bs-upload>
                    {{ format_bytes(space.upload_max_size,'MiB') }}
                </dd>
            </dl>
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block body %}
            <div class='d-flex align-items-center justify-content-end'>
                <div class='me-4'>
                    费用：<span class='text-primary'>￥<span data-bs-price class='fs-2'>0.00</span></span>
                </div>
                <div>
                    <button type='submit' class='btn btn-primary px-3' disabled>去支付</button>
                </div>
            </div>
        {% endblock %}
    {% endembed %}
    <input name='size' type='hidden' value='0' />
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/billing/size",{{ days }});
    </script>
{% endblock %}
