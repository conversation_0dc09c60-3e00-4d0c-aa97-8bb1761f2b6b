{% extends "org/billing/layout.twig" %}
{% block form_body %}
    {% embed "components/card.twig" %}
        {% block body %}
            <dl class='row'>
                <dt class='col-2'>开通时长</dt>
                <dd class='col-10'>
                    <div class="btn-group">
                        {% for years in 1..3 %}
                            <input
                                type="radio"
                                class="btn-check"
                                name="years"
                                id="year_{{ years }}"
                                value="{{ years }}"
                                {{ loop.first?'checked' }}
                            />
                            <label class="btn btn-outline-primary px-4" for="year_{{ years }}">{{ years }}年</label>
                        {% endfor %}
                    </div>
                </dd>
            </dl>
            <dl class='row mb-0'>
                <dt class='col-2'>续费后到期时间</dt>
                <dd data-bs-time class='col-10 text-primary'></dd>
            </dl>
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block body %}
            <div class='d-flex align-items-center justify-content-end'>
                <div class='me-4'>
                    费用：<span class='text-primary me-1'>￥<span data-bs-price class='fs-2'>0.00</span></span>
                    <i data-bs-price-detail class="bi bi-question-circle text-muted" role='button'></i>
                </div>
                <div>
                    <button type='submit' class='btn btn-primary px-3'>去支付</button>
                </div>
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import(
            "org/billing/plan/buy",
            "{{ space.expire_time }}",
            parseInt("{{ constant('app\\lib\\goods\\Plan::PRICES')[name] }}"),
            parseInt("{{ constant('app\\lib\\goods\\Size::PRICE')*space.size }}"),
            parseInt("{{ constant('app\\lib\\goods\\Tokens::PRICE')*space.tokens }}")
        );
    </script>
{% endblock %}
