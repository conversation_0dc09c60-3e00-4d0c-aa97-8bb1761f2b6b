{% extends "org/layout.twig" %}
{% block page_aside %}
    <nav data-bs-sidebar data-strict class='sidebar'>
        <div class='header'>成员管理<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/invite.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a></div>
        <ul>
            <li>
                <a href="/-/org/member">所有成员</a>
            </li>
        </ul>
        <div class='title d-flex justify-content-between'>
            团队 ({{ space.owner.teams.count() }})
            <a data-bs-toggle="modal" href="#team-modal"><i class="bi bi-plus-square"></i></a>
        </div>
        <ul>
            {% for item in space.owner.teams %}
                <li>
                    <a class='d-flex justify-content-between' href="/-/org/member/team/{{ item.id }}">
                        {{ item.name }}
                        <span class='text-black-50'>#{{ item.id }}</span>
                    </a>
                </li>
            {% endfor %}
        </ul>
    </nav>
{% endblock %}
{% block after_body %}
    <div id='team-modal' class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建团队</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action='/-/org/team' method='post' data-bs-form>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="col-form-label">团队名称</label>
                            <input type="text" name='name' class="form-control" placeholder='团队名称' />
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
