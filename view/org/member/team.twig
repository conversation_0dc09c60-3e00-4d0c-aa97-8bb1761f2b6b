{% extends "org/member/layout.twig" %}
{% block web_title %}
    成员管理
{% endblock %}
{% block page_title %}
    {{ team.name }} ({{ members.count() }})
{% endblock %}
{% block page_action %}
    <div class="dropdown">
        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            团队管理
        </button>
        <ul class="dropdown-menu shadow">
            <li>
                <a class="dropdown-item" data-bs-toggle="modal"
                   data-bs-target="#member-team-modal" href="#">添加成员</a></li>
            <li>
            <li>
                <a class="dropdown-item" data-bs-toggle="modal"
                   data-bs-target="#update-team-modal" href="#">编辑团队</a></li>
            <li>
                <hr class="dropdown-divider">
            </li>
            <li><a class="dropdown-item link-danger" data-bs-confirm data-message='确定要删除吗？' data-method='delete'
                   href="/-/org/team/{{ team.id }}">删除团队</a></li>
        </ul>
    </div>
    <div id='update-team-modal' class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑团队</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action='/-/org/team/{{ team.id }}' method='put' data-bs-form>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="col-form-label">团队名称</label>
                            <input type="text" name='name' class="form-control" value='{{ team.name }}'
                                   placeholder='团队名称' />
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id='member-team-modal' class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加成员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action='/-/org/team/{{ team.id }}/member' method='post' data-bs-form>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div data-bs-toggle='selection'
                                 data-search
                                 data-source='/-/org/member/search'
                                 data-multiple class='selection dropdown'>
                                <input name="members" value="" type="hidden" />
                                <div class="dropdown-toggle">
                                    <div class="text default">选择成员</div>
                                </div>
                                <ul class="dropdown-menu shadow">

                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">确定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
{% block page_content %}
    {% embed "components/table.twig" %}
        {% block thead %}
            <tr>
                <th>账号</th>
                <th width='200'>邮箱</th>
                <th width='100' class='text-end'>操作</th>
            </tr>
        {% endblock %}
        {% block tbody %}
            {% for member in members %}
                <tr>
                    <td>
                        <span class='d-flex align-items-center'>
                            <img class='me-2 rounded-circle' src='{{ member.avatar }}' height='24' width='24' />
                            {{ member.name }}
                            {% if member.id == user.id %}
                                <span class="badge bg-secondary ms-1">你自己</span>
                            {% endif %}
                            {% if member.pivot.block_time %}
                                <span class="badge bg-danger ms-1">已禁用</span>
                            {% endif %}
                        </span>
                    </td>
                    <td>{{ member.email }}</td>
                    <td class='text-end'>
                        {% include 'org/member/action.twig' %}
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td class='text-center text-muted' colspan='5'>暂无成员</td>
                </tr>
            {% endfor %}
        {% endblock %}
    {% endembed %}
    {{ members.render()|raw }}
{% endblock %}
{% block scripts %}

{% endblock %}
