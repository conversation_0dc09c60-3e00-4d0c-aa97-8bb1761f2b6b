<div class="dropdown">
    <button class="btn btn-light btn-sm dropdown-toggle" type="button"
            data-bs-toggle="dropdown">
        <i class="bi bi-three-dots-vertical"></i>
    </button>
    <ul class="dropdown-menu dropdown-menu-end shadow">
        <li>
            <a class="dropdown-item"
               data-bs-toggle="modal"
               data-bs-target="#member-team-modal-{{ member.id }}"
               href="#">设置团队</a>
        </li>
        {% if member.id != user.id and can(user,'admin',space, member) %}
            {% if member.pivot.block_time %}
                <li>
                    <a class="dropdown-item"
                       data-bs-confirm
                       data-message='确定要取消禁用吗？'
                       data-method='post'
                       href="/-/org/member/{{ member.id }}/unblock">取消禁用</a>
                </li>
            {% else %}
                <li>
                    <a class="dropdown-item"
                       data-bs-confirm
                       data-message='禁用后该用户将无法访问空间，随时可以取消禁用<br />确定要禁用吗？'
                       data-method='post'
                       href="/-/org/member/{{ member.id }}/block">禁用用户</a>
                </li>
            {% endif %}
            <li>
                <hr class="dropdown-divider">
            </li>
            <li>
                <a class="dropdown-item text-danger"
                   data-bs-confirm
                   data-message='删除后该用户将退出所有的团队以及文档<br />确定要删除吗？'
                   data-method='delete'
                   href="/-/org/member/{{ member.id }}">删除用户</a>
            </li>
        {% endif %}
    </ul>
</div>
<div id='member-team-modal-{{ member.id }}' class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">设置团队</h5>
                <button type="button" class="btn-close"
                        data-bs-dismiss="modal"></button>
            </div>
            <form action='/-/org/member/{{ member.id }}/team' method='post'
                  data-bs-form>
                <div class="modal-body">
                    <div class="mb-3">
                        <div data-bs-toggle='selection' data-multiple
                             class='selection dropdown'>
                            {% set ids = member.teams|column('id') %}
                            <input name="teams" value="{{ ids|join(',') }}"
                                   type="hidden" />
                            <div class="dropdown-toggle">
                                <div class="text default">选择所属团队</div>
                            </div>
                            <ul class="dropdown-menu shadow">
                                {% for team in space.owner.teams %}
                                    <li class="dropdown-item"
                                        data-value='{{ team.id }}'>
                                        {{ team.name }}
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                            data-bs-dismiss="modal">取消
                    </button>
                    <button type="submit" class="btn btn-primary">确定</button>
                </div>
            </form>
        </div>
    </div>
</div>
