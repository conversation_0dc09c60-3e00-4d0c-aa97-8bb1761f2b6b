{% extends "org/member/layout.twig" %}
{% block web_title %}
    成员管理
{% endblock %}
{% block page_title %}
    所有成员 ({{ members.count() }})
{% endblock %}
{% block page_action %}
    <div class="dropdown">
        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            添加成员
        </button>
        <ul class="dropdown-menu shadow">
            <li>
                <a data-bs-invite data-message='确定要创建邀请链接吗？' class="dropdown-item"
                   data-method='post'
                   href="/-/org/member/invite">链接批量邀请</a></li>
            <li>
                <hr class="dropdown-divider">
            </li>
            <li><a class="dropdown-item" href="/-/org/member/invite">查看邀请记录</a></li>
        </ul>
    </div>
{% endblock %}
{% block page_content %}
    {% embed "components/table.twig" %}
        {% block thead %}
            <tr>
                <th>账号</th>
                <th width='200'>邮箱</th>
                <th width='150'>加入时间</th>
                <th width='100'>角色</th>
                <th>团队</th>
                <th width='100' class='text-end'>操作</th>
            </tr>
        {% endblock %}
        {% block tbody %}
            {% for member in members %}
                <tr>
                    <td>
                        <span class='d-flex align-items-center'>
                            <img class='me-2 rounded-circle' src='{{ member.avatar }}' height='24' width='24' />
                            {{ member.name }}
                            {% if member.id == user.id %}
                                <span class="badge bg-secondary ms-1">你自己</span>
                            {% endif %}
                            {% if member.pivot.block_time %}
                                <span class="badge bg-danger ms-1">已禁用</span>
                            {% endif %}
                        </span>
                    </td>
                    <td>{{ member.email }}</td>
                    <td>{{ member.pivot.create_time }}</td>
                    <td>
                        {% if member.id == user.id or not can(user,'admin',space, member) %}
                            {{ member.pivot.role }}
                        {% else %}
                            <div
                                data-bs-toggle='selection'
                                data-bs-member
                                data-url='/-/org/member/{{ member.id }}/role'
                                data-method='post'
                                class='selection dropdown'
                            >
                                <div class="dropdown-toggle">
                                    <div class="text">
                                        {{ member.pivot.role }}
                                    </div>
                                </div>
                                <ul class="dropdown-menu shadow">
                                    {% if can(user,'owner',space) %}
                                        <li class="dropdown-item" data-text='创始人' data-value='60'>
                                            <div class='py-2'>
                                                <h6>创始人</h6>
                                                <p class='mb-0 text-muted fs-7'>拥有所有权限</p>
                                            </div>
                                        </li>
                                        <li class="dropdown-item" data-text='管理员' data-value='50'>
                                            <div class='py-2'>
                                                <h6>管理员</h6>
                                                <p class='mb-0 text-muted fs-7'>拥有空间管理权限</p>
                                            </div>
                                        </li>
                                    {% endif %}
                                    <li class="dropdown-item" data-text='创作者' data-value='40'>
                                        <div class='py-2'>
                                            <h6>创作者</h6>
                                            <p class='mb-0 text-muted fs-7'>拥有文档创建权限</p>
                                        </div>
                                    </li>
                                    <li class="dropdown-item" data-text='阅读者' data-value='20'>
                                        <div class='py-2'>
                                            <h6>阅读者</h6>
                                            <p class='mb-0 text-muted fs-7'>拥有阅读、参与文档权限</p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        {% endif %}
                    </td>
                    <td>
                        {% set names = member.teams|column('name') %}
                        {{ names|join(',') }}
                    </td>
                    <td class='text-end'>
                        {% include 'org/member/action.twig' %}
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td class='text-center text-muted' colspan='5'>暂无成员</td>
                </tr>
            {% endfor %}
        {% endblock %}
    {% endembed %}
    {{ members.render()|raw }}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/invite");
        window.import("org/member");
    </script>
{% endblock %}
