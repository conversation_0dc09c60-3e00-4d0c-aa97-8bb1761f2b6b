{% extends "org/layout.twig" %}
{% block page_title %}
    HTTPS配置
{% endblock %}
{% block page_content %}
    <form data-bs-form action='/-/org/domain/https' method='post'>
        <div class="mb-3 row">
            <label class="col-2 col-form-label">开启HTTPS</label>
            <div class="col-10 d-flex align-items-center">
                <div class="form-check form-switch">
                    <input type="hidden" name="enable" value="off" />
                    <input class="form-check-input" type="checkbox" name='enable'
                           value='on' {{ domain.cert_id?'checked' }} />
                </div>
            </div>
        </div>
        <div class="mb-3 row" {{ not domain.cert_id?'hidden' }}>
            <label class="col-2 col-form-label">域名关联的证书</label>
            <div class="col-10">
                <div class='selection dropdown' data-source='/-/org/domain/https/cert' data-bs-toggle='selection'>
                    <input type="hidden" name="cert_id" value="{{ domain.cert_id }}" />
                    <div class="dropdown-toggle">
                        <div class="text default">
                            选择证书
                        </div>
                    </div>
                    <ul class="dropdown-menu shadow"></ul>
                </div>
                <div class="form-text">
                    您也可以选择添加一本新的证书再进行关联。<a class='link-primary' href='{{ user.cloud.ssl_url }}' target='_blank'>新增证书»</a>
                </div>
            </div>
        </div>
        <div class="row">
            <label class="col-2 col-form-label"></label>
            <div class="col-10">
                <button class='btn btn-primary' type='submit'>保存</button>
            </div>
        </div>
    </form>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/https");
    </script>
{% endblock %}
