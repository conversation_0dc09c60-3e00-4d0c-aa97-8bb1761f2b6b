{% extends "org/layout.twig" %}
{% block web_title %}
    域名绑定
{% endblock %}
{% block page_title %}
    域名绑定<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/bind.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a>
{% endblock %}
{% block page_body %}
    {% if space.parked_domain %}
        {% embed "components/card.twig" %}
            {% block header %}
                当前已绑定域名：
                <a class='link-primary' href='{{ space.parked_domain.url }}' target='_blank'>
                    {{ space.parked_domain.name }}
                </a>
            {% endblock %}
            {% block body %}
                {% if info is null %}
                    <div class="alert alert-warning">
                        域名绑定信息查询失败，可重新绑定
                    </div>
                {% else %}
                    {% if info.status == -1 %}
                        <div class='alert alert-danger'>
                            域名未通过审核，请检查域名是否备案
                        </div>
                    {% endif %}
                    <dl class='row'>
                        <dd class='col-1'>状态：</dd>
                        <dd class='col-11'>
                            {% if info.status == -1 %}
                                <i class="bi bi-x-circle-fill text-danger"></i>
                            {% elseif info.status == 2 %}
                                <i class="bi bi-record-circle-fill text-info"></i>
                            {% elseif info.status == 0 %}
                                <i class="bi bi-dash-circle-fill text-muted"></i>
                            {% else %}
                                <i class="bi bi-check-circle-fill text-success"></i>
                            {% endif %}
                            {{ info.message }}
                        </dd>
                    </dl>
                    {% if info.status == 1 %}
                        <dl class='row'>
                            <dd class='col-1'>HTTPS：</dd>
                            <dd class='col-11'>
                                {% if info.https %}
                                    <i class="bi bi-check-circle-fill text-success"></i> 已开启
                                {% else %}
                                    <i class="bi bi-dash-circle-fill text-muted"></i> 未开启
                                {% endif %}
                                <a class='ms-2 link-primary' href='/-/org/domain/https'>设置</a>
                            </dd>
                        </dl>
                        <dl class='row align-items-start'>
                            <dd class='col-1'>公共CDN：</dd>
                            <dd class='col-11'>
                                <div class="form-check form-switch">
                                    <input
                                        data-bs-cdn
                                        data-url='/-/org/domain/cdn'
                                        data-method='post'
                                        class="form-check-input"
                                        type="checkbox"
                                        {{ space.parked_domain.cdn?'checked' }}
                                    />
                                </div>
                                <div class="form-text">
                                    开启后文档中的图片、视频等媒体资源将使用公共CDN，能够降低绑定域名的CDN流量消耗，但可能会影响访问速度，按需开启
                                </div>
                            </dd>
                        </dl>
                    {% endif %}
                {% endif %}
                <button
                    data-bs-confirm
                    data-message='确定要解除绑定吗？'
                    data-url='/-/org/domain'
                    data-method='delete'
                    class='btn btn-secondary mt-3'>
                    解除绑定
                </button>
            {% endblock %}
        {% endembed %}
    {% else %}
        {% embed "components/card.twig" %}
            {% block body %}
                <div class="alert alert-info">
                    <strong>仅支持已备案的域名绑定</strong>，
                    绑定前请在域名 DNS 设置中添加一条 CNAME 记录指向别名 <code>{{ space.cname }}</code>
                </div>
                <form method='post' data-bs-form>
                    <div class='mb-3'>
                        <input type="text" name='name' value='' class="form-control" placeholder='自定义域名' />
                    </div>
                    <button type="submit" class="btn btn-primary">检测并保存</button>
                </form>
            {% endblock %}
        {% endembed %}
    {% endif %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("org/domain");
    </script>
{% endblock %}
