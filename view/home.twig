{% extends "layout.twig" %}
{% block web_title %}
    {% if is_saas() %}
        {{ space.title }}
    {% endif %}
{% endblock %}

{% block header %}
    <header>
        <nav class="navbar bg-white border-bottom fixed-top px-3">
            <div class="container-fluid">
                <div class='logo fs-5 py-1 w-auto'>
                    {{ space.title }}
                </div>
                <div class='flex-fill'></div>
                {% if can(user,'read',space) %}
                    <a class="btn btn-primary" href='{{ space.url }}'>
                        进入工作台
                    </a>
                {% endif %}
            </div>
        </nav>
    </header>
{% endblock %}
{% block main %}
    <div class='container page-body'>
        <div class='py-4 text-center mb-3'>
            <p class='mb-4'>
                <img class='rounded-circle' src="{{ space.logo }}" width='150' height='150' />
            </p>
            <h4 class='fw-normal'>{{ space.title }}</h4>
        </div>
        <div class='card border-0 shadow-sm'>
            <div class='card-body'>
                <table class="table align-middle mb-0">
                    <tbody>
                    {% for book in books %}
                        <tr>
                            <td width='50'>
                                <img class='rounded' src='{{ book.logo }}' width='48' height='48' />
                            </td>
                            <td>
                                <a href="{{ book.uri }}" target='_blank'>
                                    {{ book.name }}
                                </a>
                            </td>
                            <td class="text-black-50 text-center" width='150'>
                                更新于 {{ book.release_time.diffForHumans() }}
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td class='text-muted text-center border-0' colspan='3'>暂无公开文档</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                {% set pages = books.render() %}
                {% if pages %}
                    <div class='mt-3'>
                        {{ pages|raw }}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
{% block page_footer %}

{% endblock %}

{% block extra_scripts %}

{% endblock %}
