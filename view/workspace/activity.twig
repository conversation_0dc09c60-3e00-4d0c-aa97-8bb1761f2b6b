{% extends "workspace/layout.twig" %}
{% block page_title %}
    动态
{% endblock %}
{% block page_content %}
    {% for activity in activities %}
        <div class="d-flex border-bottom pt-3">
            <img class="bd-placeholder-img flex-shrink-0 me-3 rounded-circle" src='{{ activity.user.avatar }}'
                 width='32'
                 height='32' />
            <div class='pb-3 flex-fill'>
                <p class="mb-1 d-flex justify-content-between">
                    <strong class="text-gray-dark">{{ activity.user.name }}</strong>
                    <span class='text-black-50'>{{ activity.create_time.diffForHumans() }}</span>
                </p>
                {{ activity.html|raw }}
            </div>
        </div>
    {% else %}
        <div class='text-secondary'>暂无动态</div>
    {% endfor %}
    {% if activities.hasPages() %}
        <div class='pt-3'>
            {{ activities.render()|raw }}
        </div>
    {% endif %}
{% endblock %}
