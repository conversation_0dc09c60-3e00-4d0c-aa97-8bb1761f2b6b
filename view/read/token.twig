{% extends "layout.twig" %}
{% block header %}

{% endblock %}
{% block main %}
    <div class='container page-body'>
        <div class="row justify-content-center">
            <div class="col col-12 col-md-8 col-lg-5">
                {% embed 'components/card.twig' %}
                    {% block body %}
                        <div class='d-flex align-items-center mb-3'>
                            <img src="{{ book.logo }}" class="rounded me-2" width='45' height='45' />
                            <h4 class='mb-0'>{{ book.name }}</h4>
                        </div>
                        <div class="alert alert-warning">
                            {% if not user and space.isSSOAvailable() and is_parked %}
                                此文档为私有或内部文档，请输入<strong>文档令牌</strong>或使用
                                <strong>企业账号</strong>登录后继续阅读
                            {% else %}
                                此文档为私有或内部文档，请输入<strong>文档令牌</strong>继续阅读
                            {% endif %}
                        </div>
                        <form method='post' action='/@{{ book.hash_id }}/token' data-bs-form>
                            <div class="mb-3">
                                <label class="form-label">阅读令牌</label>
                                <input name='token' type="text" class="form-control" placeholder='请输入文档令牌' />
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">打开文档</button>
                            </div>
                        </form>
                        {% if not user and space.isSSOAvailable() and is_parked %}
                            <div class="d-flex justify-content-center mt-2 ">
                                OR
                            </div>
                            <div class="d-grid mt-2">
                                <a href='/-/saml/login' class="btn btn-light">使用企业账号登录</a>
                            </div>
                        {% endif %}
                    {% endblock %}
                {% endembed %}
            </div>
        </div>
    </div>
{% endblock %}
{% block extra_scripts %}

{% endblock %}
