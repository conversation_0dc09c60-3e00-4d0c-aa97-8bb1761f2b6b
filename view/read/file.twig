<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{{ title }}</title>
    {% if keywords %}
        <meta name="keywords" content="{{ keywords }}" />
    {% endif %}
    {% if description %}
        <meta name="description" content="{{ description }}" />
    {% endif %}
    {% if user is defined and user is not empty %}
        <meta name="ttc-token" content="{{ user.getCloudToken() }}" />
    {% endif %}
</head>
<body>
<div id="root"></div>
<script type="application/payload+json">
    {{ payload|json_encode(constant('JSON_UNESCAPED_UNICODE'))|raw }}
</script>
{% for script in scripts %}
    <script src="{{ script }}"></script>
{% endfor %}
<script type="text/javascript">
    TopWrite.bootstrap();
</script>
</body>
</html>
