{% extends "book/layout.twig" %}
{% block web_title %}
    文档成员
{% endblock %}
{% block page_title %}
    文档成员<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/permission.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a>
{% endblock %}
{% block page_action %}
    <button data-bs-toggle="modal" data-bs-target="#member-modal" class="btn btn-primary" type="button">
        添加成员
    </button>
    <div id='member-modal' class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加成员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action='/-/book/{{ book.hash_id }}/member' method='post' data-bs-form>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div data-bs-toggle='selection'
                                 data-search
                                 data-source='/-/book/{{ book.hash_id }}/member/search'
                                 data-multiple
                                 class='selection dropdown'>
                                <input name="members" value="" type="hidden" />
                                <div class="dropdown-toggle">
                                    <div class="text default">输入团队名或用户名（必须是空间成员）</div>
                                </div>
                                <ul class="dropdown-menu shadow"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
{% block page_content %}
    <table class="table table-hover align-middle">
        <thead>
        <tr>
            <th>名称</th>
            <th>加入时间</th>
            <th width='200'>角色</th>
            <th class='text-end'>操作</th>
        </tr>
        </thead>
        <tbody>
        {% for member in members %}
            <tr>
                <td>
                    <span class='d-flex align-items-center'>
                        <img class='me-2 rounded-circle' src='{{ member.avatar }}' height='24' width='24' />
                        {{ member.name }}
                        {% if member.is(user) %}
                            <span class="badge bg-secondary ms-1">你自己</span>
                        {% endif %}
                    </span>
                </td>
                <td>{{ member.create_time }}</td>
                <td>
                    {% if member.is(user) %}
                        {{ member.role }}
                    {% else %}
                        <div
                                data-bs-toggle='selection'
                                data-bs-member
                                data-url='/-/book/{{ book.hash_id }}/member/{{ member.id }}/role'
                                data-method='post'
                                class='selection dropdown'
                        >
                            <div class="dropdown-toggle">
                                <div class="text">
                                    {{ member.role }}
                                </div>
                            </div>
                            <ul class="dropdown-menu shadow">
                                {% if member.is('app\\model\\User') %}
                                    <li class="dropdown-item" data-text='管理员' data-value='50'>
                                        <div class='py-2'>
                                            <h6>管理员</h6>
                                            <p class='mb-0 text-muted fs-7'>拥有所有权限</p>
                                        </div>
                                    </li>
                                {% endif %}
                                <li class="dropdown-item" data-text='写作者' data-value='30'>
                                    <div class='py-2'>
                                        <h6>写作者</h6>
                                        <p class='mb-0 text-muted fs-7'>拥有文档写作、阅读权限</p>
                                    </div>
                                </li>
                                <li class="dropdown-item" data-text='阅读者' data-value='20'>
                                    <div class='py-2'>
                                        <h6>阅读者</h6>
                                        <p class='mb-0 text-muted fs-7'>仅有阅读权限</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    {% endif %}
                </td>
                <td class='text-end'>
                    {% if not member.is(user) %}
                        <a class='text-danger' data-bs-confirm data-message='确定要将该成员移出吗？' data-method='delete'
                           href='/-/book/{{ book.hash_id }}/member/{{ member.id }}'>移出</a>
                    {% endif %}
                </td>
            </tr>
        {% else %}
            <tr>
                <td class='text-center text-muted' colspan='5'>暂无成员</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("book/member");
    </script>
{% endblock %}
