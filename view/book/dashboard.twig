{% extends "book/layout.twig" %}
{% block page_title %}
    文档概览
{% endblock %}
{% block page_action %}
    <div class="dropdown">
        <a type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
            克隆
        </a>
        <div class="dropdown-menu dropdown-menu-end shadow clone-menu">
            <form class="px-4 py-3">
                {% if book.ssh_url %}
                    <div class="mb-3">
                        <label class="form-label">
                            使用SSH克隆
                            <a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/clone.html'
                               target='_blank'><i class="bi bi-question-circle-fill"></i></a>
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" readonly value='{{ book.ssh_url }}' />
                            <button data-bs-copy class="btn btn-outline-secondary" type="button">
                                <i class="bi bi-clipboard"></i>
                            </button>
                        </div>
                    </div>
                {% endif %}
                <div class="mb-3">
                    <label class="form-label">
                        使用HTTPS克隆
                        <a class='ms-2' data-tas-doc='maximum'
                           href='https://doc.topthink.com/knowledge/clone.html'
                           target='_blank'><i class="bi bi-question-circle-fill"></i></a>
                    </label>
                    <div class="input-group">
                        <input type="text" class="form-control" readonly value='{{ book.git_url }}' />
                        <button data-bs-copy class="btn btn-outline-secondary" type="button">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="btn-group">
        <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            下载
        </button>
        <ul class="dropdown-menu dropdown-menu-end shadow">
            {% from 'macro/book.twig' import download %}
            {{ download(book,'pdf',"PDF") }}
            {{ download(book,'epub',"ePub") }}
            {{ download(book,'word',"Word") }}
            {{ download(book,'html',"HTML") }}
        </ul>
    </div>
    {% if book.release_time %}
        <a type="button" class="btn btn-secondary" target='_blank' href="{{ book.web_url }}">
            <i class="bi bi-eye"></i> 阅读
        </a>
    {% else %}
        <span class="d-inline-block" tabindex="0" data-bs-toggle="tooltip" title="尚未发布">
            <button type="button" class="btn btn-secondary" disabled>
                <i class="bi bi-eye"></i> 阅读
            </button>
        </span>
    {% endif %}
    {% if can(user,'write',book) %}
        <a type="button" class="btn btn-primary" target='_blank' href="/-/book/{{ book.hash_id }}/edit">
            <i class="bi bi-pencil-square"></i> 编辑
        </a>
    {% endif %}
{% endblock %}
{% block page_content %}
    {% for activity in activities %}
        <div class="d-flex border-bottom pt-3">
            <img class="bd-placeholder-img flex-shrink-0 me-3 rounded-circle" src='{{ activity.user.avatar }}'
                 width='32'
                 height='32' />
            <div class='pb-3 flex-fill'>
                <p class="mb-1 d-flex justify-content-between">
                    <strong class="text-gray-dark">{{ activity.user.name }}</strong>
                    <span class='text-black-50'>{{ activity.create_time.diffForHumans() }}</span>
                </p>
                {{ activity.html|raw }}
            </div>
        </div>
    {% endfor %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("copy");
    </script>
{% endblock %}
