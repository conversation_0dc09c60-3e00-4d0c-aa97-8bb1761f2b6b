{% extends "book/layout.twig" %}
{% block page_title %}
    付费阅读
{% endblock %}
{% block page_body %}
    {# 付费阅读开关和价格设置 #}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>功能设置</h5>
            <hr />
            <form method='post' action='/-/book/{{ book.hash_id }}/premium' data-bs-form>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name='premium_enabled' id="premium_enabled" />
                    <label class="form-check-label" for="premium_enabled">开启付费阅读</label>
                    <div class="form-text">
                        开启后，非成员用户付费后可阅读此文档的内容。
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">文档价格</label>
                    <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input type="number" name="price" class="form-control" placeholder="0.00" value="" />
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    保存设置
                </button>
            </form>
        {% endblock %}
    {% endembed %}

    {# 销售统计 #}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>销售统计</h5>
            <hr />
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-primary mb-1">156</h4>
                        <small class="text-muted">总销量</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-success mb-1">¥4,664.40</h4>
                        <small class="text-muted">总收入</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-info mb-1">12</h4>
                        <small class="text-muted">今日销量</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-warning mb-1">89</h4>
                        <small class="text-muted">本月销量</small>
                    </div>
                </div>
            </div>

            {# 收益管理 #}
            <div class="d-flex align-items-center justify-content-between p-4 bg-light rounded">
                <div>
                    <h5 class="mb-2">未结算金额</h5>
                    <h3 class="text-primary mb-1">¥2,186.50</h3>
                </div>
                <div class="text-end">
                    <button type="button" class="btn btn-primary btn-lg" id="withdraw-btn">
                        <i class="bi bi-box-arrow-up-right me-1"></i>申请提现
                    </button>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 销售记录 #}
    {% embed "components/card.twig" %}
        {% block title %}
            销售记录
        {% endblock %}
        {% block body %}
            <table class="table table-hover align-middle mb-3">
                <thead>
                <tr>
                    <th>购买用户</th>
                    <th width='100'>支付金额</th>
                    <th width='180'>购买时间</th>
                    <th width='180' class="text-end">操作</th>
                </tr>
                </thead>
                <tbody>
                {# 示例数据 - 实际使用时会从控制器传入 #}
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="https://via.placeholder.com/32x32/007bff/ffffff?text=U" class="rounded-circle me-2" width="32" height="32" />
                            <div>
                                <div class="fw-medium">张三</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-medium">¥29.90</span>
                    </td>
                    <td>
                        <div>2024-12-01 14:30</div>
                    </td>
                    <td class="text-end">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                    data-bs-toggle="dropdown">
                                操作
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-order-id="1" data-action="detail">
                                        <i class="bi bi-eye"></i> 查看详情
                                    </a></li>
                                <li><a class="dropdown-item text-danger" href="#" data-order-id="1"
                                       data-action="refund" data-bs-confirm data-message="确定要退款吗？">
                                        <i class="bi bi-arrow-counterclockwise"></i> 申请退款
                                    </a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>

            {# 分页 - 实际使用时会从控制器传入 #}

            <ul class="pagination mb-0">
                <li class="page-item disabled">
                    <span class="page-link">上一页</span>
                </li>
                <li class="page-item active">
                    <span class="page-link">1</span>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>

    </script>
{% endblock %}
