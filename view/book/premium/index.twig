{% extends "book/layout.twig" %}
{% block page_title %}
    付费阅读
{% endblock %}
{% block page_body %}
    {# 付费阅读开关和价格设置 #}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>付费阅读设置</h5>
            <hr />
            <div class="form-check form-switch mb-3">
                <input data-bs-selector='change' class="form-check-input" type="checkbox"
                       name='premium_enabled'
                       id="premium_enabled" />
                <label class="form-check-label" for="premium_enabled">开启付费阅读</label>
                <div class="form-text">
                    开启后，用户需要付费才能阅读此文档的内容。
                </div>
            </div>

            <div id="price-settings" class="d-none">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">文档价格</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text">¥</span>
                            <input type="number" name="price" class="form-control"
                                   placeholder="0.00" step="0.01" min="0.01" max="999.99"
                                   value="" />
                        </div>
                        <div class="form-text">
                            设置用户购买此文档需要支付的价格（元）
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">试读页数</label>
                        <input type="number" name="free_pages" class="form-control mb-3"
                               placeholder="3" min="0" max="50"
                               value="3" />
                        <div class="form-text">
                            允许用户免费阅读的页数，0表示不允许试读
                        </div>
                    </div>
                </div>

                <button type="button" id="save-settings" class="btn btn-primary">
                    <i class="bi bi-check"></i> 保存设置
                </button>
            </div>
        {% endblock %}
    {% endembed %}

    {# 销售统计 #}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>销售统计</h5>
            <hr />
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-primary mb-1" data-stat="total_sales">0</h4>
                        <small class="text-muted">总销量</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-success mb-1" data-stat="total_revenue">¥0.00</h4>
                        <small class="text-muted">总收入</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-info mb-1" data-stat="today_sales">0</h4>
                        <small class="text-muted">今日销量</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 mb-3">
                        <h4 class="text-warning mb-1" data-stat="month_sales">0</h4>
                        <small class="text-muted">本月销量</small>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 销售记录 #}
    {% embed "components/card.twig" %}
        {% block title %}
            销售记录
        {% endblock %}
        {% block body %}
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex gap-2">
                    <select class="form-select form-select-sm" style="width: auto;" id="status-filter">
                        <option value="">全部状态</option>
                        <option value="paid">已支付</option>
                        <option value="pending">待支付</option>
                        <option value="refunded">已退款</option>
                    </select>
                    <input type="date" class="form-control form-control-sm" style="width: auto;" id="date-filter" />
                </div>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="export-sales">
                    <i class="bi bi-download"></i> 导出记录
                </button>
            </div>

            <table class="table table-hover align-middle">
                <thead>
                <tr>
                    <th>购买用户</th>
                    <th>订单号</th>
                    <th>支付金额</th>
                    <th>支付状态</th>
                    <th>购买时间</th>
                    <th class="text-end">操作</th>
                </tr>
                </thead>
                <tbody>
                    {# 示例数据 - 实际使用时会从控制器传入 #}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="https://via.placeholder.com/32x32/007bff/ffffff?text=U" class="rounded-circle me-2" width="32" height="32" />
                                <div>
                                    <div class="fw-medium">张三</div>
                                    <small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>ORD20241201001</code>
                        </td>
                        <td>
                            <span class="fw-medium">¥29.90</span>
                        </td>
                        <td>
                            <span class="badge bg-success">已支付</span>
                        </td>
                        <td>
                            <div>2024-12-01 14:30</div>
                            <small class="text-muted">2小时前</small>
                        </td>
                        <td class="text-end">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                    操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-order-id="1" data-action="detail">
                                        <i class="bi bi-eye"></i> 查看详情
                                    </a></li>
                                    <li><a class="dropdown-item text-danger" href="#" data-order-id="1"
                                           data-action="refund" data-bs-confirm data-message="确定要退款吗？">
                                        <i class="bi bi-arrow-counterclockwise"></i> 申请退款
                                    </a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="https://via.placeholder.com/32x32/28a745/ffffff?text=L" class="rounded-circle me-2" width="32" height="32" />
                                <div>
                                    <div class="fw-medium">李四</div>
                                    <small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>ORD20241201002</code>
                        </td>
                        <td>
                            <span class="fw-medium">¥29.90</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">待支付</span>
                        </td>
                        <td>
                            <div>2024-12-01 13:15</div>
                            <small class="text-muted">3小时前</small>
                        </td>
                        <td class="text-end">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                    操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-order-id="2" data-action="detail">
                                        <i class="bi bi-eye"></i> 查看详情
                                    </a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <img src="https://via.placeholder.com/32x32/dc3545/ffffff?text=W" class="rounded-circle me-2" width="32" height="32" />
                                <div>
                                    <div class="fw-medium">王五</div>
                                    <small class="text-muted"><EMAIL></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <code>ORD20241130003</code>
                        </td>
                        <td>
                            <span class="fw-medium">¥29.90</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">已退款</span>
                        </td>
                        <td>
                            <div>2024-11-30 16:45</div>
                            <small class="text-muted">1天前</small>
                        </td>
                        <td class="text-end">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                        data-bs-toggle="dropdown">
                                    操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-order-id="3" data-action="detail">
                                        <i class="bi bi-eye"></i> 查看详情
                                    </a></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            {# 分页 - 实际使用时会从控制器传入 #}
            <div class="d-flex justify-content-center">
                <nav>
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link">上一页</span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("book/premium", "/-/book/{{ book.hash_id }}");
    </script>
{% endblock %}
