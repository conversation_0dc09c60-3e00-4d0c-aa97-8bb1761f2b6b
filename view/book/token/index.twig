{% extends "book/layout.twig" %}
{% block page_title %}
    访问令牌
{% endblock %}
{% block page_action %}
    <a href='/-/book/{{ book.hash_id }}/token/create' class='btn btn-primary'>
        <i class="bi bi-plus"></i> 新建令牌
    </a>
{% endblock %}
{% block page_body %}
    {% set new_token = session('new_token') %}
    {% if new_token %}
        <div class='card border-success shadow-sm mb-3'>
            <div class='card-body'>
                <label class='form-label'>您新创建的文档令牌</label>
                <div class="input-group">
                    <input type="text" class="form-control" value='{{ new_token }}' readonly />
                    <button data-bs-copy data-message='令牌已复制到剪贴板' class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <div class='form-text'>请确保妥善保存它 - 您无法再次访问它的内容。</div>
            </div>
        </div>
    {% endif %}
    {% embed "components/card.twig" %}
        {% block body %}
            <table class="table table-hover align-middle">
                <thead>
                <tr>
                    <th>令牌名称</th>
                    <th>范围</th>
                    <th>创建于</th>
                    <th>最近使用</th>
                    <th>到期</th>
                    <th class='text-end'>操作</th>
                </tr>
                </thead>
                <tbody>
                {% for token in tokens %}
                    <tr>
                        <td>{{ token.name }}</td>
                        <td>{{ token.scopes_name }}</td>
                        <td>{{ token.create_time.diffForHumans() }}</td>
                        <td>{{ token.last_time?token.last_time.diffForHumans():'从不' }}</td>
                        <td>{{ token.expire_time?token.expire_time.diffForHumans():'从不' }}</td>
                        <td class='text-end'>
                            <a data-bs-confirm data-method='delete' data-message='确定要撤销码？'
                               href='/-/book/{{ book.hash_id }}/token/{{ token.id }}'>撤销</a>
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan='6' class='text-muted text-center'>暂无令牌</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% endblock %}
    {% endembed %}
{% endblock %}

{% block scripts %}
    <script type='text/javascript'>
        window.import("copy");
    </script>
{% endblock %}
