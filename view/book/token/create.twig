{% extends "book/layout.twig" %}
{% block page_title %}
    新建令牌
{% endblock %}
{% block page_content %}
    <form method='post' action='/-/book/{{ book.hash_id }}/token' data-bs-form>
        <div class='mb-3'>
            <label class='form-label'>名称</label>
            <input type="text" name='name' class="form-control" placeholder='令牌名称' />
        </div>
        <div class='mb-3'>
            <label class='form-label'>到期时间</label>
            <input type="date" name='expire_time' class="form-control" placeholder='YYYY-MM-DD' />
        </div>
        <div class='mb-3'>
            <label class='form-label'>选择范围</label>
            <div class='text-muted mb-3'>范围设置授予令牌的权限级别。</div>
            <div class="form-check">
                <input class="form-check-input" name='scopes[]' type="checkbox" value="read_book" id="scope_read_book">
                <label class="form-check-label" for="scope_read_book">
                    文档阅读
                </label>
                <div class='form-text text-muted mb-2'>用于私有或内部文档的分享阅读</div>
            </div>
            <div class="form-check">
                <input class="form-check-input" name='scopes[]' type="checkbox" value="read_repository"
                       id="scope_read_repository">
                <label class="form-check-label" for="scope_read_repository">
                    文档数据(读取)
                </label>
                <div class='form-text text-muted mb-2'>
                    授予文档Git仓库的只读权限，可用于文档导入
                </div>
            </div>
            <div class="form-check">
                <input class="form-check-input" name='scopes[]' type="checkbox" value="write_repository"
                       id="scope_write_repository">
                <label class="form-check-label" for="scope_write_repository">
                    文档数据(读写)
                </label>
                <div class='form-text text-muted mb-2'>
                    授予文档Git仓库的读写权限
                </div>
            </div>
        </div>

        <button class='btn btn-primary' type='submit'>保存</button>
    </form>
{% endblock %}
