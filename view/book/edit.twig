{% extends "layout.twig" %}
{% block page_title %}
    编辑文档
{% endblock %}
{% block body %}
    <main class="vh-100 d-flex">
        {% if book.status == 1 %}
            <div class="flex-fill d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <form name="editorForm" action="{{ url }}" method="post">
                <input type="hidden" name="channel" value="{{ channel }}" autocomplete="off" />
                <input type="hidden" name="token" value="{{ token }}" autocomplete="off" />
            </form>
        {% elseif book.status == 0 or book.status > 1 %}
            <div class="flex-fill d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p data-bs-check data-url='/-/book/{{ book.hash_id }}/check' class='text-muted mt-3'>文档初始化中……</p>
            </div>
        {% else %}
            <div class="flex-fill d-flex flex-column align-items-center justify-content-center">
                <i class="bi bi-exclamation-circle me-2 fs-2 text-muted"></i>
                <h5 class='text-muted mt-4'>文档初始化失败</h5>
                <p class='text-muted mt-4'>
                    <button data-bs-api data-method='post' data-url='/-/book/{{ book.hash_id }}/retry' class='btn btn-primary'>重试</button>
                </p>
            </div>
        {% endif %}
    </main>
{% endblock %}
{% block scripts %}
    {% if book.status == 1 %}
        <script type='text/javascript'>
            document.editorForm.submit();
        </script>
    {% elseif book.status== 0 or book.status > 1 %}
        <script type='text/javascript'>
            window.import("book/check");
        </script>
    {% endif %}
{% endblock %}
{% block extra_scripts %}

{% endblock %}
