{% extends "layout.twig" %}
{% block page_title %}
    创建文档
{% endblock %}
{% block main %}
    <div class='container page-body'>
        <div class="row justify-content-center mt-5">
            <div id='select' class='col col-12 col-lg-6'>
                {% embed "components/card.twig" %}
                    {% block content %}
                        <h4 class='card-header text-center py-3'>
                            创建新的文档<a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/create.html' target='_blank'><i class="bi bi-question-circle-fill"></i></a>
                        </h4>
                        <div class='card-body'>
                            <div class="list-group list-group-flush">
                                {% if space.isOrg() %}
                                {% else %}
                                <div class='alert alert-info'>个人空间禁止企业文档及商业使用，否则可能导致空间禁用，参考<a href="https://doc.topthink.com/knowledge/price.html" target="_blank">版本区别</a>及<a href="https://doc.topthink.com/knowledge/lisence.html" target="_blank">用户协议</a> </div>
                                {% endif %}
                                <a class="list-group-item list-group-item-action py-3" href='#empty'>
                                    <h5 class="card-title">创建空白文档</h5>
                                    <p class="card-text text-secondary">
                                        创建一个空白文档，开始新的创作
                                    </p>
                                </a>
                                <a class="list-group-item list-group-item-action py-3" href='#template'>
                                    <h5 class="card-title">从模板创建</h5>
                                    <p class="card-text text-secondary">
                                        从预置的模板中选择一个创建文档
                                    </p>
                                </a>
                                <a class="list-group-item list-group-item-action py-3" href='#token'>
                                    <h5 class="card-title">从文档令牌导入</h5>
                                    <p class="card-text text-secondary">
                                        通过文档令牌导入文档
                                    </p>
                                </a>
                                <a class="list-group-item list-group-item-action py-3" href='#git'>
                                    <h5 class="card-title">从GIT导入</h5>
                                    <p class="card-text text-secondary">
                                        从GIT版本库(如 Github、Bitbucket 或 Gitlab) 迁移数据
                                    </p>
                                </a>
                                <a class="list-group-item list-group-item-action py-3" href='#kancloud'>
                                    <h5 class="card-title">从看云导入</h5>
                                    <p class="card-text text-secondary">
                                        迁移看云的文档到知识管理，可选择空间批量迁移
                                    </p>
                                </a>
                            </div>
                        </div>
                    {% endblock %}
                {% endembed %}
            </div>
            <div id='content' hidden>
                {% embed "components/card.twig" %}
                    {% block body %}
                        <div class="row">
                            <div class="col-4">
                                <div class='p-3'>
                                    <p>
                                        <a class='text-secondary' href='#'><i class="bi bi-chevron-left me-1"></i>返回重新选择</a>
                                    </p>
                                    <h2>创建空白文档</h2>
                                    <p>
                                        创建一个空白文档，开始新的创作，可安装插件
                                    </p>
                                </div>
                            </div>
                            <div class="col-8">
                                <div class='card-body' id='body'>
                                    <form action='/-/book/new' data-bs-form method='post'>
                                        <div class="mb-3">
                                            <label class="form-label">文档名称</label>
                                            <input type="text" name='name' class="form-control" placeholder='文档名称' />
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">可见范围</label>
                                            <div>
                                                {% for key,level in space.visibility_levels %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                               name="visibility_level"
                                                               id="visibility_level_{{ key }}"
                                                               value="{{ key }}" {{ loop.first?'checked' }} />
                                                        <label class="form-check-label"
                                                               for="visibility_level_{{ key }}">
                                                            {{ level.name }}
                                                        </label>
                                                        <div class="mb-2 form-text text-muted">
                                                            {{ level.description }}
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">创建文档</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    {% endblock %}
                {% endembed %}
            </div>
        </div>
    </div>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("book/new", "{{ url('/-/book/template') }}");
    </script>
{% endblock %}

