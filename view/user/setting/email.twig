{% extends "user/setting/layout.twig" %}
{% block page_title %}
    邮箱设置
{% endblock %}
{% block page_content %}
    <form method='post' action='/-/user/setting/email' data-bs-form>
        {% if user.email %}
            <div class='mb-3'>
                <label class="form-label">原邮箱</label>
                <input type="text" disabled value='{{ user.email }}' class="form-control" />
            </div>
        {% endif %}
        <div class='mb-3'>
            <label class="form-label">新邮箱</label>
            <input type="text" name='email' value='' class="form-control" placeholder='新邮箱' />
        </div>
        <div class='mb-3'>
            <label class="form-label">验证码</label>
            <div class="input-group">
                <input type="text" name='code' class="form-control" placeholder="验证码" />
                <button
                    data-bs-send-code
                    data-url='/-/user/setting/email/code'
                    data-method='post'
                    class="btn btn-outline-secondary"
                    type="button"
                >
                    发送验证码
                </button>
            </div>
        </div>
        {% if user.password %}
            <div class='mb-3'>
                <label class="form-label">账号密码</label>
                <input type="password" name='password' class="form-control" placeholder='账号密码' />
            </div>
        {% endif %}
        <button type="submit" class="btn btn-primary">保存修改</button>
    </form>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("user/setting/email");
    </script>
{% endblock %}
