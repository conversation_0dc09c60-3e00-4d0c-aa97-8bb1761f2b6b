{% extends "user/setting/layout.twig" %}
{% block page_title %}
    SSH公钥
{% endblock %}
{% block page_action %}
    <button data-bs-toggle="modal" data-bs-target="#ssh-modal" class='btn btn-primary'>
        <i class="bi bi-plus"></i> 添加SSH公钥
    </button>
    <div id='ssh-modal' class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加SSH公钥</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action='/-/user/setting/ssh' method='post' data-bs-form>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="col-form-label">公钥名称</label>
                            <input type="text" name='title' class="form-control" placeholder='公钥名称' />
                        </div>
                        <div class="mb-3">
                            <label class="col-form-label">公钥内容</label>
                            <textarea rows='4' class="form-control" name='key'
                                      placeholder='请粘贴以 ssh-rsa 开头的公钥'></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
{% block page_body %}
    {% if count(keys)>0 %}
        <div class="list-group border-0 shadow-sm">
            {% for key in keys %}
                <div class="list-group-item d-flex align-items-center p-3">
                    <div class='me-4'><i class="bi bi-key fs-1"></i></div>
                    <div class='flex-fill'>
                        <h5 class="mb-1">{{ key.title }}</h5>
                        <p class="mb-0"><small class='text-secondary'>{{ key.fingerprint }}</small></p>
                        <p class="mb-0"><small class='text-muted'>{{ key.create_time.diffForHumans() }}</small></p>
                    </div>
                    <a data-bs-confirm href='/-/user/setting/ssh/{{ key.id }}' data-message='确定要删除吗？'
                       data-method='delete'
                       class='btn btn-light'>删除</a>
                </div>
            {% endfor %}
        </div>
    {% else %}
        {% embed "./components/card.twig" %}
            {% block body %}
                暂无数据
            {% endblock %}
        {% endembed %}
    {% endif %}
{% endblock %}
{% block scripts %}

{% endblock %}
