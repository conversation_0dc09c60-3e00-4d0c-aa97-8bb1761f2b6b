{% extends "user/setting/layout.twig" %}
{% block page_title %}
    基本信息
{% endblock %}
{% block page_body %}
    {% embed "./components/card.twig" %}
        {% block body %}
            <form method='post' action='/-/user/setting/info' data-bs-form>
                <div class='alert alert-danger' hidden></div>
                <div class='mb-3'>
                    <label class="form-label">昵称</label>
                    <input type="text" name='name' value='{{ user.name }}' class="form-control" placeholder='昵称' />
                </div>
                <div class='mb-3'>
                    <label for="name" class="form-label">头像</label>
                    <div class='d-flex flex-row' data-bs-avatar>
                        <img class='border rounded-circle' width='56' height='56' src='{{ user.avatar }}' />
                        <div class='ms-3'>
                            <button type='button' class='btn btn-secondary btn-sm'>选择图片</button>
                            <input name='avatar' hidden type='file' accept="image/*" />
                            <p class='mb-0'>
                                <small class='text-muted'>支持文件格式：.png, .jpg...</small>
                            </p>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">保存修改</button>
            </form>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("user/setting/info");
    </script>
{% endblock %}
