{% extends "user/setting/layout.twig" %}
{% block page_title %}
    密码设置
{% endblock %}
{% block page_content %}
    <form method='post' action='/-/user/setting/password' data-bs-form>
        {% if user.password %}
            <div class='mb-3'>
                <label class="form-label">旧密码</label>
                <input type="password" name='old_password' value='' class="form-control" placeholder='旧密码' />
            </div>
        {% endif %}
        <div class='mb-3'>
            <label class="form-label">新密码</label>
            <input type="password" name='password' value='' class="form-control" placeholder='新密码' />
        </div>
        <div class='mb-3'>
            <label class="form-label">确认密码</label>
            <input type="password" name='password_confirm' value='' class="form-control" placeholder='确认密码' />
        </div>
        <button type="submit" class="btn btn-primary">保存修改</button>
    </form>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("user/setting/info");
    </script>
{% endblock %}
