{% extends "user/layout.twig" %}
{% block container %}
    {% embed 'components/card.twig' %}
        {% block body %}
            <form data-bs-form action='/-/user/register' method='post'>
                <div class="mb-3">
                    <label class="form-label">邮箱</label>
                    <input name='email' type="text" class="form-control" />
                </div>
                <div class='mb-3'>
                    <label class="form-label">验证码</label>
                    <div class="input-group">
                        <input type="text" name='code' class="form-control" placeholder="验证码" />
                        <button
                                data-bs-send-code
                                data-url='/-/user/register/code'
                                data-method='post'
                                class="btn btn-outline-secondary"
                                type="button"
                        >
                            发送验证码
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">密码</label>
                    <input name='password' type="password" class="form-control" />
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">注册</button>
                </div>
            </form>
        {% endblock %}
    {% endembed %}
    <p class='mt-2'>已有账号? <a href='/-/user/login'>登录</a></p>
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("user/register");
    </script>
{% endblock %}
