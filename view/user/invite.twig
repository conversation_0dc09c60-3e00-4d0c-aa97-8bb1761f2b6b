{% extends "user/layout.twig" %}
{% block container %}
    {% embed 'components/card.twig' %}
        {% block body %}
            <div class="d-flex justify-content-center pt-4 mb-3">
                <img src="{{ space.logo }}" class="rounded" width='100' height='100' />
            </div>
            <form method='post' data-bs-form>
                <p class="card-text text-center text-muted">{{ invite.user.name }}邀请您加入</p>
                <h5 class="card-title text-center mb-3">{{ space.name }}</h5>
                <div class='d-grid'>
                    {% if invite.available %}
                        <button type='submit' class="btn btn-primary">立即加入</button>
                    {% else %}
                        {% if space.members_max_num and space.members_num>=space.members_max_num %}
                            <button type='submit' disabled class="btn btn-primary">该空间成员已满，请联系空间管理员</button>
                        {% else %}
                            <button type='submit' disabled class="btn btn-primary">该邀请链接已失效</button>
                        {% endif %}
                    {% endif %}
                </div>
            </form>
        {% endblock %}
    {% endembed %}
{% endblock %}
