<?php

namespace app\notification;

use app\model\User;
use yunwuxin\Notification;
use yunwuxin\notification\message\Mail;

class ResetPassword extends Notification
{
    public $token;

    public function __construct($token)
    {
        $this->token = $token;
    }

    public function channels($notifiable)
    {
        return ['mail'];
    }

    /**
     * @param User $notifiable
     * @return Mail
     */
    public function toMail($notifiable)
    {
        return (new Mail())
            ->subject('找回密码')
            ->line('您收到此电子邮件，是因为我们收到了您的帐户的密码重置请求。')
            ->action('重置密码', url('/-/user/password/edit', ['token' => $this->token])->domain(true))
            ->to($notifiable->email)
            ->line('此邮件30分钟内有效，如果您没有请求密码重置，则忽略此邮件。');
    }
}
