<?php

namespace app\controller;

use app\lib\SamlUser;
use Exception;
use think\saml\IdentityProvider;
use yunwuxin\Auth;

class SamlController extends SpaceController
{
    public function metadata()
    {
        $auth = $this->space->getSamlAuth();

        return response($auth->getSPMetadata())->contentType('application/xml');
    }

    public function login()
    {
        if (!$this->space->isSSOAvailable()) {
            abort(404);
        }
        $auth = $this->space->getSamlAuth();

        $idp = IdentityProvider::fromXml($this->space->getSamlIdp());

        return $auth->login($idp, $this->request->header('Referer'));
    }

    public function acs(Auth $auth)
    {
        if (!$this->space->isSSOAvailable()) {
            abort(404);
        }
        try {
            $url  = $this->request->param('RelayState', '/');
            $saml = $this->space->getSamlAuth();

            $idp = IdentityProvider::fromXml($this->space->getSamlIdp());

            $response = $saml->acs($idp, $this->request);

            $attributes = $response->getAttributes();

            $user = new SamlUser([
                'id'     => $response->getNameId(),
                'name'   => $attributes['name'][0] ?? '',
                'avatar' => $attributes['avatar'][0] ?? '',
                'team'   => $attributes['team'][0] ?? '',
            ]);

            $auth->login($user);

            return redirect($url);
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }
}
