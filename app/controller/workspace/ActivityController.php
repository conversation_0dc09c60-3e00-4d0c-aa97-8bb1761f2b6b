<?php

namespace app\controller\workspace;

use app\model\Book;
use app\model\Team;
use app\model\User;
use think\db\Query;

class ActivityController extends Controller
{
    public function index()
    {
        $query = $this->space->activities()->order('create_time desc');

        if ($this->space->isOrg() && !$this->user->can('admin', $this->space->owner)) {
            //非管理员只能看到自己有权限的文档动态
            $query->where(function (Query $query) {
                $query->whereNull('book_id', 'OR');
                $query->whereIn('book_id', function (Query $query) {
                    $query
                        ->table('book')
                        ->where(function (Query $query) {
                            $query->whereIn('id', function (Query $query) {
                                $query
                                    ->table('book_member')
                                    ->where(function (Query $query) {
                                        $query
                                            ->whereOr(function (Query $query) {
                                                $query
                                                    ->where('member_id', $this->user->id)
                                                    ->where('member_type', User::class);
                                            });

                                        $query
                                            ->whereOr(function (Query $query) {
                                                $query
                                                    ->whereIn('member_id', function (Query $query) {
                                                        $query->table('team_member')
                                                              ->join('team', 'team.id=team_member.team_id')
                                                              ->where('team.org_id', $this->space->owner->id)
                                                              ->where('team_member.user_id', $this->user->id)
                                                              ->field('team_id');
                                                    })
                                                    ->where('member_type', Team::class);
                                            });
                                    })
                                    ->field('book_id');
                            }, 'OR');
                            $query->whereOr('visibility_level', '>=', Book::LEVEL_PROTECTED);
                        })
                        ->field('id');
                }, 'OR');
            });
        }

        $activities = $query->paginate(10);

        return view('workspace/activity')->assign('activities', $activities);
    }
}
