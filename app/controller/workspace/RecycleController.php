<?php

namespace app\controller\workspace;

use app\event\BookRestored;
use app\lib\Date;
use app\model\Member;
use app\model\User;
use think\db\Query;
use think\Event;

class RecycleController extends Controller
{

    protected function getBookQuery()
    {
        return $this->space
            ->books()
            ->onlyTrashed()
            ->whereTime('delete_time', '>', Date::now()->sub(7, 'days'));
    }

    public function index()
    {
        $query = $this->getBookQuery()->order('delete_time desc');

        if ($this->space->isOrg() && !$this->user->can('admin', $this->space->owner)) {
            //非组织管理员时，需要检查文档权限
            $query->whereIn('id', function (Query $query) {
                $query
                    ->table('book_member')
                    ->where('member_id', $this->user->id)
                    ->where('member_type', User::class)
                    ->where('access_level', '>=', Member::MASTER)
                    ->field('book_id');
            });
        }

        $books = $query->paginate();

        return view('workspace/recycle')->assign(compact('books'));
    }

    public function restore(Event $event, $id)
    {
        $book = $this->getBookQuery()->findOrFail($id);

        $this->authorized('admin', $book);

        $book->restore();

        $event->trigger(new BookRestored($this->user, $book));
    }
}
