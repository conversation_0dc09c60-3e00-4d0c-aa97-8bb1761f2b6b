<?php

namespace app\controller\workspace;

class BookController extends Controller
{
    public function index($scope = 'all')
    {
        $query = $this->space->books()
            ->order('update_time desc')
            ->userBooks($this->space, $this->user, $scope);

        if ($this->request->has('name')) {
            $query->whereLike('name', "%{$this->request->param('name')}%");
        }

        $books = $query->with('space')->paginate();

        return view('workspace/book')->assign(compact('books'));
    }

}
