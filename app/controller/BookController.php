<?php

namespace app\controller;

use app\lib\Hashids;
use app\model\Book;
use think\App;
use think\View;
use yunwuxin\Auth;

abstract class BookController extends SpaceController
{
    protected ?Book $book = null;

    public function __construct(App $app, Auth $auth, View $view)
    {
        parent::__construct($app, $auth, $view);

        $id = $this->request->route('book');
        if ($id) {
            $this->book = $this->resolveBook(Hashids::decode($id));

            if ($this->request->isGet()) {
                $view->assign('book', $this->book);
            }
        }
    }

    /**
     * @param $id
     * @return Book
     */
    protected function resolveBook($id)
    {
        return $this->space->books()->findOrFail($id);
    }
}
