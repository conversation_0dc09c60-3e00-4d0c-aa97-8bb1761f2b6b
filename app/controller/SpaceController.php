<?php

namespace app\controller;

use app\model\Space;
use app\model\User;
use think\App;
use think\View;
use yunwuxin\Auth;

abstract class SpaceController extends ViewController
{
    protected ?Space $space;

    public function __construct(App $app, Auth $auth, View $view)
    {
        parent::__construct($app, $auth, $view);

        $this->space = $this->request->getSpace();

        if (is_saas()) {
            //检查域名
            if (!$this->space) {
                //主域名打开
                $auth->authenticate();

                $space = $this->user->getLastSpace();
                if (!$space) {
                    //创建空间
                    abort(redirect(url('/-/org/new')));
                }
                abort(redirect(url('/-')->domain($space->domain)));
            }

            //记录当前
            if ($this->user instanceof User) {
                $this->user->setLastSpace($this->space);
            }
        }

        if ($this->request->isGet()) {
            $view->assign('space', $this->space);

            if (!$this instanceof ReadController && !$this->request->isJson()) {
                //检查空间是否进入删除流程
                if ($this->space->isDeleting()) {
                    abort(view('org/deleting'));
                }
            }
        }
    }
}
