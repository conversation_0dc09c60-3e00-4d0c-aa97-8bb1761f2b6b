<?php

namespace app\controller\admin;

use app\model\Setting;
use ArrayObject;
use think\exception\ValidateException;
use think\helper\Arr;

class SettingController extends Controller
{
    public function read($name)
    {
        $setting = Setting::read($name);

        return json(new ArrayObject($setting));
    }

    public function update($name)
    {
        $data = json_decode($this->request->getContent(), true);

        switch ($name) {
            case 'login':
                if (!Arr::get($data, 'qrcode', true)) {
                    if (!$this->user->email || !$this->user->password) {
                        throw new ValidateException('关闭"扫码登录"前，请先绑定邮箱并设置密码');
                    }
                }
                break;
        }

        Setting::write($name, $data);
    }
}
