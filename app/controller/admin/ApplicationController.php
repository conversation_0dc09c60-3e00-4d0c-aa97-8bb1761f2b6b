<?php

namespace app\controller\admin;

use app\model\Application;

class ApplicationController extends Controller
{
    public function index()
    {
        return Application::order('id desc')->select();
    }

    public function read($id)
    {
        return Application::findOrFail($id);
    }

    public function save()
    {
        $data = $this->validate([
            'name'            => 'require',
            'redirect_uri'    => 'require',
            'is_confidential' => 'boolean',
        ]);

        $data['client_id']     = md5(uniqid());
        $data['client_secret'] = md5(uniqid());

        Application::create($data);
    }

    public function update($id)
    {
        $application = Application::findOrFail($id);

        $data = $this->validate([
            'name'            => 'require',
            'redirect_uri'    => 'require',
            'is_confidential' => 'boolean',
        ]);

        $application->save($data);
    }
}
