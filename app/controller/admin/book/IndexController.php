<?php

namespace app\controller\admin\book;

use app\controller\admin\Controller;
use app\lib\Date;
use app\model\Book;

class IndexController extends Controller
{
    public function index()
    {
        $query = Book::order('id desc')->spaceExist();

        $this->searchField($query, 'id|name');

        return $query->paginate()->append(['url'], true);
    }

    public function read($id)
    {
        return Book::findOrFail($id)->append(['hash_path']);
    }

    public function block($id)
    {
        $book = Book::findOrFail($id);

        $book->save([
            'block_time' => Date::now(),
        ]);
    }

    public function unblock($id)
    {
        $book = Book::findOrFail($id);

        $book->save([
            'block_time' => null,
        ]);
    }
}
