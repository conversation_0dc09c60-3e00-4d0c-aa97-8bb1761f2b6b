<?php

namespace app\controller\admin\book;

use app\controller\admin\Controller;
use app\lib\Date;
use app\model\Activity;
use app\model\Book;
use app\model\BookArticle;

class ModerationController extends Controller
{
    public function index()
    {
        return BookArticle::bookExist()
            ->where('book_article.status', 0)
            ->with(['book'])
            ->append(['book.url'])
            ->order('book_article.update_time desc')
            ->paginate();
    }

    public function mark()
    {
        $data = $this->validate([
            'ids'       => 'require',
            'operation' => 'require|in:pass,block,review',
        ]);

        $articles = BookArticle::bookExist()
            ->where('book_article.status', 0)
            ->whereIn('book_article.id', $data['ids'])
            ->select();

        switch ($data['operation']) {
            case 'pass':
                $articles->update([
                    'status' => 1,
                ]);
                break;
            case 'block':
                $books = $articles->column('book_id');

                Book::update(
                    [
                        'block_time' => Date::now(),
                    ],
                    ['id' => $books]
                );

                break;
            case 'review':
                $articles->update([
                    'status' => 2,
                ]);

                foreach ($articles as $article) {
                    //记录文本审核动态
                    $article->book->space->createActivity(
                        null,
                        Activity::TYPE_BOOK_ARTICLE_CHECK_FAIL,
                        $article->book,
                        $article->remark,
                        $article
                    );
                }

                break;
        }
    }

}
