<?php

namespace app\controller\admin\book;

use app\controller\admin\Controller;
use app\model\Book;
use Qdrant\Http\GuzzleClient;
use Qdrant\Models\Filter\Condition\MatchString;
use Qdrant\Models\Filter\Filter;
use Qdrant\Qdrant;
use think\App;
use think\helper\Arr;
use think\View;
use yunwuxin\Auth;

class ArticleController extends Controller
{

    /** @var \app\model\Book */
    protected $book;

    public function __construct(App $app, Auth $auth, View $view)
    {
        parent::__construct($app, $auth, $view);

        $id = $this->request->route('book');

        if ($id) {
            $this->book = Book::findOrFail($id);
        }
    }

    public function index()
    {
        return $this->book->articles()->paginate();
    }

    public function content($id)
    {
        /** @var \app\model\BookArticle $article */
        $article = $this->book->articles()->findOrFail($id);

        return $this->book->getPages()->readAsJson($article->path . '.json');
    }

    public function part($id)
    {
        /** @var \app\model\BookArticle $article */
        $article = $this->book->articles()->findOrFail($id);

        $qdrantHost = env('QDRANT_HOST', 'topthink-qdrant');
        $config     = new \Qdrant\Config($qdrantHost);
        $qdrant     = new Qdrant(new GuzzleClient($config));

        $filter = new Filter();

        $filter->addMust(new MatchString('book', $this->book->hash_id));
        $filter->addMust(new MatchString('ref', $article->ref));

        $body = [
            'filter' => $filter->toArray(),
            'limit'  => 100,
        ];

        $httpFactory = new \GuzzleHttp\Psr7\HttpFactory();
        $request     = $httpFactory->createRequest('POST', "/collections/space@{$this->book->space->hash_id}/points/scroll");
        $request     = $request->withBody(
            $httpFactory->createStream(json_encode($body, JSON_THROW_ON_ERROR))
        );
        $response    = $qdrant->execute($request);

        return Arr::get($response, 'result.points');
    }
}
