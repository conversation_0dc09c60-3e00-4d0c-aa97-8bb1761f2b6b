<?php

namespace app\controller\book;

use app\controller\BookController;
use yunwuxin\auth\middleware\Authorize;

class DashboardController extends BookController
{
    protected function initialize()
    {
        parent::initialize();
        $this->middleware(Authorize::class, 'read', $this->book);
    }

    public function index()
    {
        $activities = $this->space->activities()
                                  ->order('create_time desc')
                                  ->where('book_id', $this->book->id)
                                  ->paginate();

        return view('book/dashboard')->assign('activities', $activities);
    }
}
