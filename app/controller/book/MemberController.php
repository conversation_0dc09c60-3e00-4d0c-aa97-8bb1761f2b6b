<?php

namespace app\controller\book;

use app\controller\BookController;
use app\model\BookMember;
use app\model\Member;
use app\model\Team;
use app\model\User;
use think\db\Query;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authorize;

class MemberController extends BookController
{
    protected function initialize()
    {
        parent::initialize();
        $this->middleware(Authorize::class, 'admin', $this->book);
    }

    protected function parseId($id)
    {
        [$type, $id] = explode(':', $id);
        $types = [
            'user' => User::class,
            'team' => Team::class,
        ];
        return [$types[$type], $id];
    }

    public function index()
    {
        $members = $this->book->members()->order('create_time desc')->paginate();

        return view('book/member')->assign('members', $members);
    }

    public function save()
    {
        $data = $this->validate([
            'members' => 'require',
        ]);

        foreach (explode(',', $data['members']) as $member) {

            [$type, $id] = $this->parseId($member);

            switch ($type) {
                case User::class:
                    $user = $this->space->owner->members()->find($id);
                    if ($user) {
                        $this->book->addMember($user, overwrite: false);
                    }
                    break;
                case Team::class:
                    $team = $this->space->owner->teams()->find($id);
                    if ($team) {
                        $this->book->addMember($team, overwrite: false);
                    }
                    break;
            }
        }
    }

    public function delete($id)
    {
        [$type, $id] = $this->parseId($id);
        $member = $this->book->members()->where([
            'member_id'   => $id,
            'member_type' => $type,
        ])->findOrFail();
        $member->delete();
    }

    public function role($id)
    {
        [$type, $id] = $this->parseId($id);

        /** @var BookMember $member */
        $member = $this->book->members()->where([
            'member_id'   => $id,
            'member_type' => $type,
        ])->findOrFail();

        $data = $this->validate([
            'role|角色' => 'require|in:' . Member::MASTER . ',' . Member::READER . ',' . Member::WRITER,
        ]);

        if ($type === Team::class && $data['role'] == Member::MASTER) {
            throw new ValidateException('团队不能设为管理员');
        }

        $member->save([
            'access_level' => $data['role'],
        ]);

        return response('更新成功');
    }

    public function search($q = '')
    {
        $owner = $this->space->owner;

        $teams = $owner->teams()
                       ->where(function (Query $query) use ($q) {
                           if ($q) {
                               $query->where('team.name', 'like', "%{$q}%");
                           }
                       })
                       ->limit(5)->select()
                       ->map(function (Team $team) {
                           return [
                               'text'  => $team->name,
                               'value' => "team:{$team->id}",
                               'image' => $team->avatar,
                           ];
                       });

        $users = $owner->members()
                       ->where(function (Query $query) use ($q) {
                           if ($q) {
                               $query->where('user.name', 'like', "%{$q}%");
                           }
                       })
                       ->limit(5 - $teams->count())->select()
                       ->map(function (User $member) {
                           return [
                               'text'  => $member->name,
                               'value' => "user:{$member->id}",
                               'image' => $member->avatar,
                           ];
                       });

        return json($teams->merge($users));
    }
}
