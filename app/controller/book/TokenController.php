<?php

namespace app\controller\book;

use app\controller\BookController;
use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\db\Query;
use yunwuxin\auth\middleware\Authorize;

class TokenController extends BookController
{
    protected function initialize()
    {
        parent::initialize();
        $this->middleware(Authorize::class, 'admin', $this->book);
    }

    public function index()
    {
        $tokens = $this->book->accessTokens()->where(function (Query $query) {
            $query->whereNull('expire_time', 'OR');
            $query->whereOr('expire_time', '>', Date::now());
        })->order('id desc')->select();

        return view('book/token/index')->assign('tokens', $tokens);
    }

    public function create()
    {
        return view('book/token/create');
    }

    public function save()
    {
        $data = $this->validate([
            'name|令牌名称'        => 'require',
            'expire_time|到期时间' => 'date',
            'scopes|范围'        => 'require',
        ]);

        $token = $this->book->accessTokens()->save([
            'name'        => $data['name'],
            'token'       => Uuid::uuid4()->toString(),
            'scopes'      => implode(',', $data['scopes']),
            'expire_time' => $data['expire_time'] ?: null,
        ]);

        return redirect("/-/book/{$this->book->hash_id}/token")->with('new_token', $token->token);
    }

    public function delete($id)
    {
        $this->book->accessTokens()->delete($id);
    }
}
