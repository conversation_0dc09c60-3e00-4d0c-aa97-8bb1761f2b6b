<?php

namespace app\controller;

use app\exception\BookBlockedException;
use app\exception\SpaceBlockedException;
use app\exception\SpaceExpiredException;
use app\lib\Hashids;
use app\lib\ReadToken;
use app\model\Book;
use app\Request;
use League\MimeTypeDetection\ExtensionMimeTypeDetector;
use Symfony\Component\Filesystem\Path;
use think\App;
use think\exception\HttpException;
use think\exception\ValidateException;
use think\Filesystem;
use think\helper\Arr;
use think\helper\Str;
use think\swoole\response\File;
use think\View;
use yunwuxin\Auth;
use yunwuxin\auth\exception\AuthenticationException;
use yunwuxin\auth\exception\AuthorizationException;
use function think\swoole\helper\iterator;

class ReadController extends SpaceController
{
    /** @var Book */
    protected $book;

    protected $disk;

    protected $fromSlug = false;

    public function __construct(App $app, Auth $auth, View $view, Filesystem $filesystem, protected ReadToken $readToken)
    {
        parent::__construct($app, $auth, $view);
        $this->disk = $filesystem->disk('pages');

        $id = $this->request->route('id');

        if (empty($id)) {
            if ($this->space->isOrg() && $this->space->owner->index instanceof Book) {
                $this->book = $this->space->owner->index;
            }
        } else {
            $query = $this->space->books();
            try {
                $query->where('id', Hashids::decode($id));
            } catch (HttpException) {
                $query->where('slug', $id);
                $this->fromSlug = true;
            }
            $this->book = $query->findOrFail();
        }
    }

    public function index($path)
    {
        if ($this->book) {
            return $this->file($path);
        }

        if (!$this->space->isOrg() || $this->space->owner->index === 'workspace') {
            return redirect(space_url($this->space, '/-'));
        }

        if ($path) {
            return redirect('/');
        }

        return $this->home();
    }

    /**
     * 默认主页
     */
    public function home()
    {
        if ($this->space->isExpired()) {
            throw new SpaceExpiredException;
        }

        if ($this->space->isBlocked()) {
            throw new SpaceBlockedException;
        }

        $books = $this->space->books()
            ->where('visibility_level', Book::LEVEL_PUBLIC)
            ->where('status', 1)
            ->whereNotNull('release_time')
            ->whereNull('block_time')
            ->order('release_time desc')
            ->paginate();

        return view('home')->assign('books', $books);
    }

    public function book($path)
    {
        if (!$this->fromSlug && $this->book->slug && !$this->request->isJson()) {
            //自动跳转到自定义路径
            return redirect($this->book->uri . '/' . $path);
        }
        return $this->file($path);
    }

    /**
     * 全文搜索
     */
    public function search()
    {
        if (!$this->book) {
            abort(404);
        }

        if ($this->request->isHead()) {
            return response(code: 204);
        }

        $data = $this->validate([
            'keyword' => 'require',
            'part'    => '',
        ]);

        return $this->book->getPages()->search($data['keyword'], $data['part'] ?? null);
    }

    public function ask()
    {
        if (!$this->book) {
            abort(404);
        }

        if (!$this->book->canAsk()) {
            abort(code: 403);
        }

        if ($this->request->isHead()) {
            return response(code: 204);
        }

        $data = $this->validate([
            'keyword' => 'require',
            'part'    => '',
        ]);

        $result = $this->book->getPages()->ask($data['keyword'], $data['part'] ?? null);

        $generator = function () use ($result) {
            foreach ($result as $text) {
                yield 'data: ' . json_encode($text) . "\n\n";
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    public function token()
    {
        $data = $this->validate([
            'token|文档令牌' => 'require',
        ]);

        if (!$this->readToken->check($data['token'], $this->book)) {
            throw new ValidateException(['token' => '无效令牌']);
        }
    }

    protected function file($path)
    {
        if (!can($this->user, 'browse', $this->book) && !$this->readToken->associated($this->book, $this->request)) {
            if (!$this->user && $this->space->isSSOAvailable() && $this->request->isMode(Request::DOMAIN_PARKED)) {
                throw new AuthenticationException();
            }
            throw new AuthorizationException('权限不足，禁止访问');
        }

        if ($this->space->isExpired()) {
            throw new SpaceExpiredException;
        }

        if ($this->space->isBlocked()) {
            throw new SpaceBlockedException;
        }

        if ($this->book->isBlocked()) {
            throw new BookBlockedException;
        }

        $path = $path ?: 'index.html';

        if (empty($this->book->sha)) {
            abort(404, '文档尚未发布');
        }

        if (Str::endsWith($path, '/')) {
            //禁止访问目录
            abort(404);
        }

        $eTag = "W/\"" . $this->book->sha . "\"";

        if ($this->request->header('If-None-Match') == $eTag) {
            return response()->code(304);
        }

        $pages = $this->book->getPages();

        if ($pages->has($path) && Path::getExtension($path) != 'html') {
            $response = new File($pages->path($path), autoContentType: false);

            $detector = new ExtensionMimeTypeDetector();
            $mimeType = $detector->detectMimeTypeFromPath($path);
            if ($mimeType) {
                $response->header([
                    'Content-Type' => $mimeType,
                ]);
            } else {
                $response->setAutoContentType();
            }

            //永久缓存asset下的文件
            if (Str::startsWith($path, 'asset/')) {
                $response->header([
                    'Cache-Control' => 'max-age=315360000',
                ]);
            }

            return $response;
        }

        ['id' => $id, 'lfs' => $lfs, 'scripts' => $scripts] = $pages->getContext();

        $summary = $pages->getSummary();
        $config  = $pages->getConfig();

        //构建申明
        $poweredBy = $this->space->powered_by ? [
            'name' => '顶想云',
            'link' => 'https://www.topthink.com',
        ] : false;

        //设置LFS域名
        $lfsDomain  = config('app.lfs_domain');
        $realDomain = $this->request->getRealDomain();

        if (!empty($realDomain) && !$realDomain->cdn) {
            $lfsDomain = "{$this->request->scheme()}://{$realDomain->name}";
        }

        if (!empty($lfsDomain)) {
            //兼容云盾需要添加了一个后缀
            $lfs['url'] = "{$lfsDomain}/lfs/{oid}.dat";
        }

        $payload = [
            'id'      => $id,
            'sha'     => $this->book->sha,
            'summary' => $summary,
            'config'  => $config,
            'lfs'     => $lfs,
            'options' => [
                'poweredBy' => $poweredBy,
                'weapp'     => $this->book->weapp,
            ],
        ];

        $file = $pages->readAsJson($path . '.json');

        if ($file) {
            //检查章节是否违规
            $article = $this->book->articles()->where('path', $path)->find();

            if ($article && $article->status != 1) {
                $file['content'] = '> 内容违规整改中';
                $eTag            = null;
            }

            $payload['file'] = $file;
        } else {
            $payload['file'] = [
                'path' => $path,
            ];
        }

        $article = $summary->getArticle($path);

        $title       = Arr::get($file, 'meta.title', $article ? $article->title : Arr::get($config, 'title'));
        $keywords    = Arr::get($file, 'meta.keywords');
        $description = Arr::get($file, 'meta.description');

        $scripts = array_map(function ($script) use ($path) {
            if (preg_match('/^https?:\/\//', $script)) {
                return $script;
            }
            return Path::makeRelative($script, dirname('./' . $path));
        }, $scripts);

        $response = view('read/file')->assign(compact('title', 'keywords', 'description', 'scripts', 'payload'));

        if ($eTag) {
            $response->eTag($eTag);
        }

        return $response;
    }
}
