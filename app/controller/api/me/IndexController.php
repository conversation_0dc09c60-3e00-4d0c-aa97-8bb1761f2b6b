<?php

namespace app\controller\api\me;

use app\job\ReleaseBook;
use app\model\Book;
use Symfony\Component\Filesystem\Path;
use Symfony\Component\Finder\Finder;
use think\ChunkUpload\Server;
use think\File;
use think\Filesystem;

class IndexController extends Controller
{
    public function index()
    {
        return json($this->user->visible(['id', 'email', 'avatar', 'name']));
    }

    public function release(Server $server, Filesystem $filesystem)
    {
        $disk = $filesystem->disk('release');

        return $server->serve($this->request, function (File $file, $metadata) use ($disk) {
            $extensions = Book::PACK_TYPES;

            /** @var Book $book */
            $book = $this->space->books()->findOrFail($metadata['id']);
            $path = Path::join($book->hash_path, $metadata['type']);

            //清理
            if ($disk->has($path)) {
                $files = Finder::create()
                    ->in($disk->path($path))
                    ->depth(0)
                    ->files();

                if ($book->sha) {
                    $files->notName($book->sha);
                }

                foreach ($files as $f) {
                    $disk->delete($path . '/' . $f->getFilename());
                }
            }

            $name = "{$metadata['sha']}{$extensions[$metadata['type']]}";

            $disk->putFileAs($path, $file, $name);

            if ($metadata['type'] == 'json') {
                //发布任务
                queue(ReleaseBook::class, [
                    $this->user->id,
                    $book->id,
                    $metadata['sha'],
                ], queue: 'release');
            }
        }, root_path() . 'storage/temp');
    }
}
