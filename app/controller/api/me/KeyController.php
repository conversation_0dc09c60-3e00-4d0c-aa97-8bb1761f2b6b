<?php

namespace app\controller\api\me;

use app\model\PublicKey;

class KeyController extends Controller
{
    public function read($id)
    {
        $key = PublicKey::where('user_id', $this->user->id)
            ->where('space_id', $this->space->id)
            ->where('id', $id)
            ->findOrFail();

        return json($key);
    }

    public function save()
    {
        $data = $this->validate([
            'title|标题' => 'require',
            'key|公钥'   => ['require', '/\A(ssh|ecdsa)-.*\Z/'],
        ]);

        $data['user_id']  = $this->user->id;
        $data['space_id'] = $this->space->id;

        $key = PublicKey::create($data);

        return json($key);
    }
}
