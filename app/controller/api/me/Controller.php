<?php

namespace app\controller\api\me;

use app\model\User;

abstract class Controller extends \app\controller\api\Controller
{
    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function ($request, $next) {

            if (!$this->user instanceof User) {
                abort(404);
            }

            return $next($request);
        });
    }
}
