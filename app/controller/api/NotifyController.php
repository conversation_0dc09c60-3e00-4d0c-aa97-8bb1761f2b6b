<?php

namespace app\controller\api;

use app\lib\Cloud;
use app\lib\Date;

class NotifyController extends Controller
{
    public function plan(Cloud $cloud, $name, $year)
    {
        $cloud->verifyChargeNotify($this->request);

        $expire_time = $this->space->expire_time;
        if (is_null($expire_time) || $expire_time->lt(Date::now())) {
            $expire_time = Date::now();
        }

        $this->space->expire_time = $expire_time->add("{$year} year");

        $this->space->plan = $name;
        $this->space->save();
    }

    public function upgrade(Cloud $cloud, $name)
    {
        $cloud->verifyChargeNotify($this->request);
        $this->space->plan = $name;
        $this->space->save();
    }

    public function tokens(Cloud $cloud, $tokens)
    {
        $cloud->verifyChargeNotify($this->request);

        $this->space->tokens += $tokens;
        $this->space->save();
    }

    public function size(Cloud $cloud, $size)
    {
        $cloud->verifyChargeNotify($this->request);

        $this->space->size += $size;
        $this->space->save();
    }
}
