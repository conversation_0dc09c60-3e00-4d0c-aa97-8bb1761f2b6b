<?php

namespace app\controller\api\ai;

use app\controller\api\Controller;
use think\ai\Client;
use think\helper\Arr;
use function think\swoole\helper\iterator;

class ChatController extends Controller
{

    public function completions(Client $client)
    {
        $this->authorized('ai', $this->space);

        $params = $this->request->only(['messages']);

        $result = $client->chat()->completions([
            'model'    => 'gpt-3.5-turbo',
            'messages' => $params['messages'],
        ]);

        $generator = function () use ($result) {
            foreach ($result as $data) {
                $text  = Arr::get($data, 'delta.content');
                $usage = Arr::get($data, 'usage');

                if (!is_null($text)) {
                    yield 'data: ' . json_encode($text) . "\n\n";
                }
                if ($usage) {
                    $this->space->consumeAiTokens($usage['total_tokens']);
                }
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
