<?php

namespace app\controller\api\book;

use app\model\Book;

class IndexController extends Controller
{
    public function index()
    {
        $query = $this->space->books()
            ->where('status', 1)
            ->whereNull('block_time');

        $this->searchField($query, 'name');

        $this->filterFields($query, [
            'ask' => true,
        ]);

        if (!$this->user) {
            //未登录只能获取到公开文档
            $query->where('visibility_level', Book::LEVEL_PUBLIC);
        }

        return json($query->append(['hash_id', 'logo'])->order('update_time desc')
            ->paginate($this->request->param('per_page', 10)));
    }

    public function read()
    {
        $eTag = "W/\"" . md5($this->book->update_time) . "\"";

        if ($this->request->header('If-None-Match') == $eTag) {
            return response()->code(304);
        }

        $this->authorized('browse', $this->book);

        $this->book->withAttr('permission', function () {
            return [
                'read'  => can($this->user, 'read', $this->book),
                'write' => can($this->user, 'write', $this->book),
                'admin' => can($this->user, 'admin', $this->book),
            ];
        });

        $this->book->append(['web_url', 'ssh_url', 'lfs_url', 'space', 'permission', 'hash_id', 'logo']);

        return json($this->book)->eTag($eTag);
    }

    public function dataset()
    {
        $this->authorized('browse', $this->book);

        return $this->book->getPages()->dataset();
    }

    public function search($keyword, $limit = 3, $score = 0.8)
    {
        $this->authorized('browse', $this->book);
        return $this->book->getPages()->searchByVector($keyword, null, $limit, $score);
    }

    public function lfsFiles()
    {
        return $this->book->lfsObjects()->column('oid');
    }

}
