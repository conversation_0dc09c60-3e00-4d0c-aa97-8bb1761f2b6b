<?php

namespace app\controller\api\book;

use app\lib\Hashids;
use app\model\Book;
use think\App;
use yunwuxin\Auth;

abstract class Controller extends \app\controller\api\Controller
{
    /** @var \app\model\Book */
    protected $book;

    public function __construct(App $app, Auth $auth)
    {
        parent::__construct($app, $auth);

        $id = $this->request->route('book');

        if ($id) {
            $this->book = $this->resolveBook($id);
        }
    }

    /**
     * @param $id
     * @return Book
     */
    protected function resolveBook($id)
    {
        if (!is_numeric($id)) {
            $id = Hashids::decode($id);
        }

        return $this->space->books()->findOrFail($id);
    }
}
