<?php

namespace app\controller\api\book;

use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\db\Query;
use yunwuxin\auth\middleware\Authorize;

class TokenController extends Controller
{

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(Authorize::class, 'admin', $this->book);
    }

    public function index()
    {
        $tokens = $this->book->accessTokens()->where(function (Query $query) {
            $query->whereNull('expire_time', 'OR');
            $query->whereOr('expire_time', '>', Date::now());
        })->order('id desc')->select();

        return json($tokens);
    }

    public function read($id)
    {
        return $this->book->accessTokens()->where(function (Query $query) {
            $query->whereNull('expire_time', 'OR');
            $query->whereOr('expire_time', '>', Date::now());
        })->findOrFail($id);
    }

    public function save()
    {
        $data = $this->validate([
            'name|令牌名称'        => 'require',
            'expire_time|到期时间' => 'date',
            'scopes|范围'          => 'require|array',
        ]);

        return $this->book->accessTokens()->save([
            'name'        => $data['name'],
            'token'       => Uuid::uuid4()->toString(),
            'scopes'      => implode(',', $data['scopes']),
            'expire_time' => empty($data['expire_time']) ? null : $data['expire_time'],
        ])->append(['token']);
    }

    public function delete($id)
    {
        $this->book->accessTokens()->delete($id);
    }
}
