<?php

namespace app\controller\api;

use app\BaseController;
use app\lib\TokenUser;
use app\model\Space;
use think\App;
use yunwuxin\Auth;

abstract class Controller extends BaseController
{
    protected ?Space $space;

    public function __construct(App $app, Auth $auth)
    {
        parent::__construct($app, $auth);

        $this->space = $this->request->getSpace();

        if (is_saas()) {
            //检查域名
            if (!$this->space && $this->user instanceof TokenUser) {
                //主域名打开

                //通过令牌自动判断空间
                $this->space = $this->user->getSpace();
            }
        }

        if (!$this->space) {
            abort(404);
        }
    }
}
