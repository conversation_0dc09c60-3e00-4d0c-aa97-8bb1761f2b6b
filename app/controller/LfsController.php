<?php

namespace app\controller;

use app\BaseController;
use app\model\LfsObject;
use think\Cache;
use think\Filesystem;

class LfsController extends BaseController
{
    public function read(Filesystem $filesystem, Cache $cache, $oid)
    {
        $cacheKey = 'lfs:object:' . $oid;

        $object = LfsObject::where('oid', $oid)->cache($cacheKey, 7 * 24 * 3600)->findOrFail();

        $header = [
            'X-Accel-Redirect' => "/x-lfs/{$object->filename}",
            'Cache-Control'    => 'public, max-age=315360000',
        ];

        if (empty($object->mime_type)) {
            $file     = $filesystem->disk('lfs')->path($object->filename);
            $finfo    = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file);
            if ($mimeType) {
                $object->save([
                    'mime_type' => $mimeType,
                ]);
                //更新缓存
                $cache->delete($cacheKey);
            }
        }

        if (!empty($object->mime_type)) {
            $header['Content-Type'] = $object->mime_type;
        }

        return response()->header($header);
    }
}
