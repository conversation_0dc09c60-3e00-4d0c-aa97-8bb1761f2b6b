<?php

namespace app\controller;

use app\BaseController;
use think\App;
use think\View;
use yunwuxin\Auth;

abstract class ViewController extends BaseController
{
    public function __construct(App $app, Auth $auth, View $view)
    {
        parent::__construct($app, $auth);

        if ($this->request->isGet()) {
            $view->assign('request', $this->request);
            $view->assign('user', $this->user);
        }
    }
}
