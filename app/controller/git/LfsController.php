<?php

namespace app\controller\git;

use app\model\LfsObject;
use think\ChunkUpload\Server;
use think\File;
use think\Filesystem;

class LfsController extends Controller
{
    public function serveBatch()
    {
        $operation = $this->request->post('operation');
        $objects   = $this->request->post('objects');
        $transfers = $this->request->post('transfers', []);

        if (in_array('topthink', $transfers)) {
            $transfer = 'topthink';
        } else {
            $transfer = 'basic';
        }

        if ($operation === 'upload' && !$this->space->checkQuota('media')) {
            //检查空间容量
            $response = json(['message' => '媒体空间已满，请扩容'], 413);
        } else {
            $response = match ($operation) {
                'upload' => json([
                    'transfer' => $transfer,
                    'objects'  => array_map(function ($object) use ($transfer) {
                        if (!$this->book->lfsObjects()->where('oid', $object['oid'])->find()) {
                            $url = "/{$this->book->hash_id}.git/info/lfs/objects/{$object['oid']}/{$object['size']}";

                            $object['actions'] = [
                                'upload' => [
                                    'href'   => (string) url($url)->domain($this->space->domain),
                                    'header' => [
                                        'Content-Type'      => 'application/octet-stream',
                                        'Transfer-Encoding' => 'chunked',
                                        'Authorization'     => 'Basic ' . $this->user->getBasicToken(),
                                    ],
                                ],
                            ];
                        }
                        return $object;
                    }, $objects),
                ]),
                'download' => json([
                    'transfer' => $transfer,
                    'objects'  => array_map(function ($object) {
                        if ($this->book->lfsObjects()->where('oid', $object['oid'])->find()) {
                            $object['actions'] = [
                                'download' => [
                                    'href'   => (string) url("/{$this->book->hash_id}.git/info/lfs/objects/{$object['oid']}")->domain($this->space->domain),
                                    'header' => [
                                        'Authorization' => 'Basic ' . $this->user->getBasicToken(),
                                    ],
                                ],
                            ];
                        } else {
                            $object['error'] = [
                                'code'    => 404,
                                'message' => 'Object does not exist',
                            ];
                        }

                        return $object;
                    }, $objects),
                ]),
                default => json(['message' => 'Not found'], 404),
            };
        }

        return $response->header(['Content-Type' => 'application/vnd.git-lfs+json']);
    }

    public function upload(Filesystem $filesystem, Server $server, $oid, $size)
    {
        $disk = $filesystem->disk('lfs');

        return $server->serve($this->request, function (File $file) use ($disk, $size, $oid) {
            $object = LfsObject::findOrCreate([
                'oid'  => $oid,
                'size' => $size,
            ]);

            $disk->putFileAs($object->filename, $file, '');

            $object->books()->attach($this->book);
        }, root_path() . 'storage/temp');
    }

    public function download($oid)
    {
        $object = LfsObject::where('oid', $oid)->findOrFail();

        $header = [
            'X-Accel-Redirect' => "/x-lfs/{$object->filename}",
        ];

        return response()->header($header);
    }
}
