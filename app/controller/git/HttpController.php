<?php

namespace app\controller\git;

use app\lib\Date;
use Symfony\Component\Process\InputStream;
use Symfony\Component\Process\Process;
use think\App;
use think\exception\HttpException;
use think\Response;
use yunwuxin\Auth;
use yunwuxin\auth\exception\AuthorizationException;

class HttpController extends Controller
{
    public function __construct(App $app, Auth $auth)
    {
        parent::__construct($app, $auth);
        //检查版本库状态
        if ($this->book->status != 1) {
            throw new HttpException(404);
        }

        //检查pull权限
        if (!$this->user->canPull($this->book)) {
            throw new AuthorizationException();
        }
    }

    protected function getServiceType()
    {
        return preg_replace('/^git-/', '', $this->request->param('service'));
    }

    protected function serviceRPC($service)
    {
        if ($this->request->contentType() != "application/x-git-{$service}-request") {
            throw new HttpException(401);
        }

        $payload = [
            'book' => $this->book->id,
            'user' => $this->user->getUser()?->id,
        ];

        $env = [
            'GIT_HOOKS_PAYLOAD' => base64_encode(json_encode($payload)),
        ];

        $content = $this->request->getInput();

        $input       = new InputStream();
        $contentSize = strlen($content);
        $chunkSize   = 64 * 1024; //64KB chunks

        if ($contentSize > $chunkSize) {
            $sendSize = 0;
            do {
                $input->write(substr($content, $sendSize, $chunkSize));
            } while (($sendSize += $chunkSize) < $contentSize);
        } else {
            $input->write($content);
        }

        $input->close();

        $output = $this->run($service, ['--stateless-rpc', '.'], ['input' => $input], $env);

        return response($output)
            ->header(['Content-Type' => "application/x-git-{$service}-result"]);
    }

    public function serviceUploadPack()
    {
        return $this->serviceRPC('upload-pack');
    }

    public function serviceReceivePack()
    {
        //push
        if (!$this->user->canPush($this->book)) {
            throw new AuthorizationException();
        }
        return $this->serviceRPC('receive-pack');
    }

    public function getInfoRefs()
    {
        $service = $this->getServiceType();
        if (!in_array($service, ['upload-pack', 'receive-pack'])) {
            $this->run('update-server-info');
            $response = $this->sendFile('text/plain; charset=utf-8');
        } else {
            $stdout = $this->run($service, ['--stateless-rpc', '--advertise-refs', '.']);

            $str = "# service=git-{$service}\n";

            $content = sprintf('%04x%s', strlen($str) + 4, $str);
            $content .= '0000';
            $content .= $stdout;

            $response = response($content)
                ->header(['Content-Type' => "application/x-git-{$service}-advertisement"]);
        }

        return $this->setHeaderNoCache($response);
    }

    public function getTextFile()
    {
        return $this->setHeaderNoCache($this->sendFile('text/plain'));
    }

    public function getInfoPacks()
    {
        return $this->setHeaderCacheForever($this->sendFile('text/plain; charset=utf-8'));
    }

    public function getLooseObject()
    {
        return $this->setHeaderCacheForever($this->sendFile('application/x-git-loose-object'));
    }

    public function getPackFile()
    {
        return $this->setHeaderCacheForever($this->sendFile('application/x-git-packed-object'));
    }

    public function getIdxFile()
    {
        return $this->setHeaderCacheForever($this->sendFile('application/x-git-packed-object-toc'));
    }

    protected function setHeaderCacheForever(Response $response)
    {
        $now     = Date::now();
        $expires = $now->add(31536000, 'seconds');

        return $response->header([
            'Date'          => $now->toRfc7231String(),
            'Expires'       => $expires->toRfc7231String(),
            'Cache-Control' => 'public, max-age=31536000',
        ]);
    }

    protected function setHeaderNoCache(Response $response)
    {
        return $response->header([
            'Expires'       => 'Fri, 01 Jan 1980 00:00:00 GMT',
            'Pragma'        => 'no-cache',
            'Cache-Control' => 'no-cache, max-age=0, must-revalidate',
        ]);
    }

    protected function sendFile($contentType)
    {
        $path     = $this->request->pathinfo();
        $file     = substr($path, strlen("/{$this->book->hash_id}.git"));
        $filename = $this->book->repo_path . '/' . $file;
        if (!file_exists($filename)) {
            throw new HttpException(404);
        }

        $content = file_get_contents($filename);

        return response($content)->header(['Content-Type' => $contentType]);
    }

    protected function run($command, $args = [], $options = [], $env = null)
    {
        $process = $this->getProcess($command, $args, $options, $env);
        $process->run();

        if (!$process->isSuccessful()) {
            throw new HttpException(500, $process->getErrorOutput());
        }

        return $process->getOutput();
    }

    protected function getProcess($command, $args = [], $options = [], $env = null)
    {
        $process = new Process(['git', ...(array) $command, ...$args], $this->book->repo_path, $env);

        $process->setTimeout(0);
        $process->setIdleTimeout(120);

        if (isset($options['input'])) {
            $process->setInput($options['input']);
        }

        return $process;
    }

}
