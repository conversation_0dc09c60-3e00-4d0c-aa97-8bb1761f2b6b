<?php

namespace app\controller\git;

use app\BaseController;
use app\lib\GitUser;
use app\lib\Hashids;
use app\model\Book;
use app\model\Space;
use think\App;
use think\exception\HttpException;
use yunwuxin\Auth;

abstract class Controller extends BaseController
{
    protected ?Space $space;

    protected Book $book;

    /** @var GitUser */
    protected $user;

    public function __construct(App $app, Auth $auth)
    {
        parent::__construct($app, $auth);

        $this->space = $this->request->getSpace();

        if (!$this->space) {
            abort(404);
        }

        $id = $this->request->route('book');

        $this->book = $this->space->books()->findOrFail(Hashids::decode($id));

        if (!file_exists($this->book->repo_path)) {
            throw new HttpException(404);
        }
    }
}
