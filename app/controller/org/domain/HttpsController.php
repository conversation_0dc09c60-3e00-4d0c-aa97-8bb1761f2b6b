<?php

namespace app\controller\org\domain;

use app\controller\org\Controller;
use app\lib\Cdn;
use app\lib\Cloud;
use app\model\Domain;

class HttpsController extends Controller
{
    /** @var Domain */
    protected $domain;

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function ($request, $next) {
            $this->domain = $this->space->parked_domain;
            if (empty($this->domain)) {
                abort(404);
            }
            return $next($request);
        });
    }

    public function index()
    {
        return view('org/domain/https')
            ->assign('domain', $this->domain);
    }

    public function save(Cloud $cloud, Cdn $cdn)
    {
        $data = $this->validate([
            'enable'     => '',
            'cert_id|证书' => 'require',
        ]);

        if ($data['enable'] == 'on') {
            $cert = $cloud->getUserCert($this->user, $data['cert_id']);

            $cdn->enableServiceHttps($this->domain, $cert['certificate'], $cert['key']);

            $this->domain->save([
                'cert_id' => $data['cert_id'],
            ]);
        } else {
            $cdn->disableServiceHttps($this->domain);
            $this->domain->save([
                'cert_id' => null,
            ]);
        }
        return redirect('/-/org/domain');
    }

    public function cert(Cloud $cloud)
    {
        $result = $cloud->getUserCerts($this->user, ['domain' => $this->domain->name]);

        return array_map(function ($item) {
            return [
                'text'  => "{$item['name']} ({$item['domain']})",
                'value' => $item['id'],
            ];
        }, $result);
    }
}
