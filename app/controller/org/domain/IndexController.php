<?php

namespace app\controller\org\domain;

use app\controller\SpaceController;
use app\lib\Cdn;
use app\model\Domain;
use Exception;
use think\exception\ValidateException;
use function is_valid_domain;
use function view;

class IndexController extends SpaceController
{
    public function index(Cdn $cdn)
    {
        $view = view('org/domain/index');
        if ($this->space->parked_domain) {
            $info = $cdn->getService($this->space->parked_domain);

            $view->assign('info', $info);
        }
        return $view;
    }

    public function save(Cdn $cdn)
    {
        $data = $this->validate([
            'name|域名' => ['require', function ($domain) {
                return is_valid_domain($domain);
            }],
        ]);

        /** @var Domain $domain */
        $domain = $this->space->parkedDomain()->make($data);
        if (!$this->app->isDebug()) {
            try {
                $domain->verify();
            } catch (Exception $e) {
                throw new ValidateException(['name' => $e->getMessage()]);
            }
        }
        $domain->transaction(function () use ($cdn, $domain) {
            $cdn->createService($domain);
            $domain->save();
        });
    }

    public function cdn()
    {
        $domain = $this->space->parked_domain;
        $data   = $this->validate([
            'value' => 'require|in:on,off',
        ]);

        $domain->save([
            'cdn' => $data['value'] == 'on',
        ]);

        return response('修改成功');
    }

    public function delete(Cdn $cdn)
    {
        $domain = $this->space->parked_domain;
        $domain->transaction(function () use ($cdn, $domain) {
            $cdn->deleteService($domain);
            $domain->delete();
        });
    }
}
