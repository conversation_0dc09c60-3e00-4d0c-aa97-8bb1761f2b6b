<?php

namespace app\controller\org;

use app\lib\Date;
use app\model\Member;
use app\model\Team;
use app\model\User;
use think\Db;
use think\db\Query;

class MemberController extends Controller
{
    public function index()
    {
        if ($this->request->has('team')) {
            $view = view('org/member/team');
            /** @var Team $team */
            $team = $this->org->teams()->findOrFail($this->request->param('team'));

            $members = $team->members()->paginate();
            $view->assign('team', $team);
        } else {
            $view    = view('org/member/index');
            $members = $this->org->members()->paginate();
        }
        return $view->assign('members', $members->load(['teams' => function (Query $query) {
            $query->where('org_id', $this->org->id);
        }]));
    }

    public function role($id)
    {
        /** @var User $member */
        $member = $this->org->members()->findOrFail($id);

        $in = [Member::CREATOR, Member::READER];

        if (can($this->user, 'owner', $this->space)) {
            array_push($in, Member::MASTER, Member::OWNER);
        }

        $data = $this->validate([
            'role|角色' => 'require|in:' . join(',', $in),
        ]);

        $member->pivot->save([
            'access_level' => $data['role'],
        ]);

        return response('更新成功');
    }

    public function team($id)
    {
        /** @var User $member */
        $member = $this->org->members()->findOrFail($id);

        $teams = $this->request->param('teams');

        $leaveTeams = $member->teams()->where('org_id', $this->org->id)->whereNotIn('id', $teams)->select();
        $leaveTeams->each(function (Team $team) use ($member) {
            $team->members()->detach($member);
        });
        $newTeams = $this->org->teams()->whereIn('id', $teams)->select();

        $newTeams->each(function (Team $team) use ($member) {
            if (!$team->members()->attached($member)) {
                $team->members()->attach($member);
            }
        });
    }

    /**
     * 禁用用户
     */
    public function block($id)
    {
        /** @var User $member */
        $member = $this->org->members()->findOrFail($id);

        if ($member->id != $this->user->id && $this->user->can('admin', $this->space, $member)) {
            $member->pivot->save([
                'block_time' => Date::now(),
            ]);
        }
    }

    /**
     * 取消禁用
     */
    public function unblock($id)
    {
        /** @var User $member */
        $member = $this->org->members()->findOrFail($id);

        if ($member->id != $this->user->id && $this->user->can('admin', $this->space, $member)) {
            $member->pivot->save([
                'block_time' => null,
            ]);
        }
    }

    public function delete(Db $db, $id)
    {
        /** @var User $member */
        $member = $this->org->members()->findOrFail($id);

        $db->transaction(function () use ($member, $db) {
            //删除文档成员
            $db->name('book_member')
                ->where('member_id', $member->id)
                ->where('member_type', User::class)
                ->whereIn('book_id', function (Query $query) {
                    $query->table('book')->where('space_id', $this->space->id)->field('id');
                })->delete();

            //删除团队成员
            $db->name('team_member')
                ->where('user_id', $member->id)
                ->whereIn('team_id', function (Query $query) {
                    $query->table('team')->where('org_id', $this->org->id)->field('id');
                })->delete();

            $member->pivot->delete();
        });
    }

    public function search($q = '')
    {
        $users = $this->org->members()
            ->where(function (Query $query) use ($q) {
                if ($q) {
                    $query->where('user.name', 'like', "%{$q}%");
                }
            })
            ->limit(5)->select()
            ->map(function (User $member) {
                return [
                    'text'  => $member->name,
                    'value' => $member->id,
                    'image' => $member->avatar,
                ];
            });

        return json($users);
    }
}
