<?php

namespace app\controller\org;

use app\lib\Cloud;
use Exception;
use Symfony\Component\Filesystem\Path;
use think\exception\ValidateException;

class WeappController extends Controller
{
    public function index(Cloud $cloud)
    {
        $view = view('org/weapp');
        if ($this->org->weapp_id) {
            try {
                $weapp = $cloud->getWeapp($this->org->weapp_id);

                if ($weapp['status'] == 1 && $weapp['version']) {
                    $qrcode = $cloud->getWeappQrcode($this->org->weapp_id);

                    $view->assign('qrcode', $qrcode['url']);
                }

                if ($weapp['status'] == -1) {
                    $this->org->save(['weapp_id' => null]);
                } else {
                    $view->assign('weapp', $weapp);
                }
            } catch (Exception) {

            }
        }
        return $view;
    }

    public function save(Cloud $cloud)
    {
        if (!$this->space->isEnterprisePlan(true)) {
            throw new ValidateException('此功能仅企业版可用');
        }

        if ($this->org->weapp_id) {
            throw new ValidateException('已申请过小程序,请勿重复申请');
        }

        $data = $this->validate([
            'name|企业名称'                   => 'require',
            'code_type|代码类型'              => 'require',
            'code|企业代码'                   => 'require',
            'legal_persona_wechat|法人微信号' => 'require',
            'legal_persona_name|法人姓名'     => 'require',
        ]);

        $data['ext']         = ['space' => $this->space->hash_id];
        $data['domain']      = "https://{$this->space->domain}";
        $data['template_id'] = 1;

        try {
            $result = $cloud->createWeapp($data);
        } catch (Exception $e) {
            throw new ValidateException($e->getMessage());
        }
        $this->org->save(['weapp_id' => $result['id']]);
    }

    public function update(Cloud $cloud)
    {
        $data = $this->validate([
            'name|小程序名称'      => 'require',
            'signature|小程序介绍' => 'require',
            'avatar|小程序头像'    => 'image',
        ]);

        if ($this->org->weapp_id) {
            if (!empty($data['avatar'])) {
                $data['avatar'] = base64_encode(file_get_contents($data['avatar']->getRealPath()));
            } else {
                $data['avatar'] = base64_encode(file_get_contents(Path::join(root_path(), 'asset/frontend/public/images/logo.png')));
            }

            try {
                $cloud->updateWeapp($this->org->weapp_id, $data);
            } catch (Exception $e) {
                throw new ValidateException($e->getMessage());
            }
        }
    }
}
