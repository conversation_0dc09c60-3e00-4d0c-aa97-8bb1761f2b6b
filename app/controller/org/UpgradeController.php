<?php

namespace app\controller\org;

use app\controller\SpaceController;
use app\lib\Date;
use app\model\Member;
use app\model\Organization;
use think\Db;
use think\file\UploadedFile;
use think\Filesystem;

class UpgradeController extends SpaceController
{
    public function index()
    {
        return view('org/upgrade');
    }

    public function save(Db $db, Filesystem $filesystem)
    {
        //创建组织
        $data = $this->validate([
            'name|空间名称' => 'require',
            'logo|空间Logo' => 'image',
        ]);

        if (!empty($data['logo']) && $data['logo'] instanceof UploadedFile) {
            $data['logo'] = $filesystem->disk('uploads')->putFile('logo', $data['logo']);
        }

        return $db->transaction(function () use ($data) {
            $org = Organization::create($data);
            $org->addMember($this->user, Member::OWNER);

            $space    = $this->user->space;
            $newSpace = $org->space;

            //当前用户的空间转移给组织
            $space->owner()->associate($org);

            //设为企业版试用
            $space->save([
                'plan'        => 'trial',
                'expire_time' => Date::now()->addDays(15),
            ]);

            $newSpace->delete();

            return redirect($space->url);
        });
    }
}
