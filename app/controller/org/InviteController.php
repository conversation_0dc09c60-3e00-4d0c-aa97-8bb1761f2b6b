<?php

namespace app\controller\org;

use think\exception\ValidateException;

class InviteController extends Controller
{
    public function index()
    {
        $invites = $this->org->invites()->order('id desc')->with(['user'])->paginate();
        return view('org/invite')->assign('invites', $invites);
    }

    public function save()
    {
        if (!$this->space->checkQuota('member')) {
            throw new ValidateException('该空间成员已满，不能邀请新成员');
        }

        return json($this->org->invites()->save([
            'user_id' => $this->user->id,
            'key'     => uniqid(),
        ]));
    }
}
