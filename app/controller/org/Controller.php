<?php

namespace app\controller\org;

use app\controller\SpaceController;
use app\model\Organization;
use think\App;
use think\View;
use yunwuxin\Auth;
use yunwuxin\auth\middleware\Authorize;

abstract class Controller extends SpaceController
{

    protected Organization $org;

    public function __construct(App $app, Auth $auth, View $view)
    {
        parent::__construct($app, $auth, $view);

        if (!$this->space->isOrg()) {
            abort(404);
        }

        $this->org = $this->space->owner;

        if ($this->request->isGet()) {
            $view->assign('org', $this->org);
        }

        $this->middleware(Authorize::class, 'admin', $this->space);
    }
}
