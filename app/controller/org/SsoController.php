<?php

namespace app\controller\org;

use think\exception\ValidateException;
use think\response\File;
use think\saml\IdentityProvider;

class SsoController extends Controller
{
    public function index()
    {
        return view('org/sso');
    }

    public function toggle()
    {
        $data = $this->validate([
            'sso' => 'require',
        ]);

        $this->org->save($data);

        return response('修改成功');
    }

    public function idp()
    {
        $file = $this->request->file('file');

        $auth = $this->space->getSamlAuth();

        $xml = file_get_contents($file->getRealPath());

        try {
            $idp = IdentityProvider::fromXml($xml);

            $auth->checkIdP($idp);
        } catch (\Exception) {
            throw new ValidateException('文件格式错误');
        }

        $this->org->save([
            'idp' => $xml,
        ]);

        return redirect('/-/org/sso');
    }

    public function download()
    {
        if (empty($this->org->idp)) {
            abort(404);
        }

        return (new File($this->org->idp))->isContent()->name('metadata.xml');
    }
}
