<?php

namespace app\controller\org;

use app\controller\ViewController;
use app\model\Member;
use app\model\Organization;
use think\Db;
use think\file\UploadedFile;
use think\Filesystem;

class NewController extends ViewController
{
    public function index()
    {
        return view('org/create');
    }

    public function save(Db $db, Filesystem $filesystem)
    {
        $data = $this->validate([
            'name|空间名称' => 'require',
            'logo|空间Logo' => 'image',
        ]);

        if (!empty($data['logo']) && $data['logo'] instanceof UploadedFile) {
            $data['logo'] = $filesystem->disk('uploads')->putFile('logo', $data['logo']);
        }

        /** @var Organization $org */
        $org = $db->transaction(function () use ($data) {
            $org = Organization::create($data);
            $org->addMember($this->user, Member::OWNER);

            return $org;
        });

        return redirect($org->space->url);
    }
}
