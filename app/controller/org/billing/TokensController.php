<?php

namespace app\controller\org\billing;

use app\controller\org\Controller;
use app\lib\Date;
use app\lib\goods\Tokens;

class TokensController extends Controller
{
    public function index()
    {
        $days = $this->space->expire_time->diffInDays(Date::now());
        return view('org/billing/tokens')->assign('days', $days);
    }

    public function save()
    {
        $data = $this->validate([
            'tokens|Token额度' => 'require|integer',
        ]);

        $goods = new Tokens($this->space, $data['tokens']);

        $result = $goods->purchase();
        return redirect($result['pay_url']);
    }
}
