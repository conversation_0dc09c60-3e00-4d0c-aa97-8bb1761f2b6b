<?php

namespace app\controller\org;

use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\db\Query;

class TokenController extends Controller
{

    public function index()
    {
        $tokens = $this->space->accessTokens()->where(function (Query $query) {
            $query->whereNull('expire_time', 'OR');
            $query->whereOr('expire_time', '>', Date::now());
        })->order('id desc')->select();

        return view('org/token/index')->assign('tokens', $tokens);
    }

    public function create()
    {
        return view('org/token/create');
    }

    public function save()
    {
        $data = $this->validate([
            'name|令牌名称'        => 'require',
            'expire_time|到期时间' => 'date',
            'scopes|范围'          => 'require',
        ]);

        $token = $this->space->accessTokens()->save([
            'name'        => $data['name'],
            'token'       => Uuid::uuid4()->toString(),
            'scopes'      => implode(',', $data['scopes']),
            'expire_time' => $data['expire_time'] ?: null,
        ]);

        return redirect("/-/org/token")->with('new_token', $token->token);
    }

    public function delete($id)
    {
        $this->space->accessTokens()->delete($id);
    }
}
