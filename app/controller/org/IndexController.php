<?php

namespace app\controller\org;

use app\lib\Date;
use app\model\Book;
use think\file\UploadedFile;
use think\Filesystem;

class IndexController extends Controller
{
    public function setting()
    {
        return view('org/setting');
    }

    public function update(Filesystem $filesystem)
    {
        $data = $this->validate([
            'name'  => '',
            'logo'  => 'image',
            'index' => '',
        ]);

        if (!empty($data['logo']) && $data['logo'] instanceof UploadedFile) {
            $data['logo'] = $filesystem->disk('uploads')->putFile('logo', $data['logo']);
        }

        $this->org->save($data);
    }

    public function feature()
    {
        $data = $this->validate([
            'field' => 'require',
            'value' => 'require',
        ]);

        if ($data['field'] == 'powered_by') {
            $data['value'] = !$data['value'];
        }

        $this->org->save([
            $data['field'] => $data['value'],
        ]);

        return response('修改成功');
    }

    public function book($q = '')
    {
        $query = $this->space->books()->order('update_time desc');

        if ($q) {
            $query->whereLike('name', "%{$q}%");
        }

        if ($this->request->has('value')) {
            $query->where('id', $this->request->param('value'));
        }

        return $query->limit(5)->select()->map(function (Book $book) {
            return [
                'text'  => $book->name,
                'value' => $book->id,
                'image' => $book->logo,
            ];
        });
    }

    public function delete()
    {
        $this->space->save([
            'delete_time' => Date::now(),
        ]);
    }

    public function restore()
    {
        $this->authorized('owner', $this->space);
        $this->space->save([
            'delete_time' => null,
        ]);
    }
}
