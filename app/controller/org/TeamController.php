<?php

namespace app\controller\org;

use app\model\BookMember;
use app\model\Team;
use app\model\User;
use think\Db;

class TeamController extends Controller
{
    public function save()
    {
        $data = $this->validate([
            'name' => 'require',
        ]);

        $this->org->teams()->save($data);
    }

    public function update($id)
    {
        /** @var Team $team */
        $team = $this->org->teams()->findOrFail($id);

        $data = $this->validate([
            'name' => 'require',
        ]);

        $team->save($data);
    }

    public function delete(Db $db, $id)
    {
        /** @var Team $team */
        $team = $this->org->teams()->findOrFail($id);
        $db->transaction(function () use ($team) {
            //删除成员
            $team->members()->sync([]);
            //文档成员
            BookMember::where([
                'member_id'   => $team->id,
                'member_type' => get_class($team),
            ])->delete();

            $team->delete();
        });

        return redirect('/-/org/member');
    }

    public function member($id)
    {
        /** @var Team $team */
        $team = $this->org->teams()->findOrFail($id);

        $data = $this->validate([
            'members' => 'require',
        ]);

        foreach (explode(',', $data['members']) as $member) {
            if (!$team->members()->attached($member)) {
                $team->members()->attach($member);
            }
        }
    }

}
