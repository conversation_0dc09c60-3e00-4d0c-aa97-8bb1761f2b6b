<?php

namespace app\controller\user;

use app\controller\ViewController;
use app\lib\Cloud;
use app\model\User;
use think\exception\ValidateException;
use think\Session;
use TopThinkCloud\Client;
use yunwuxin\Auth;

class LoginController extends ViewController
{
    public function index(Session $session)
    {
        if ($this->request->has('redirect_uri')) {
            $session->set('redirect_url', $this->request->param('redirect_uri'));
        }

        if ($this->user) {
            return redirect('/-')->restore();
        }

        $from  = $session->get('from');
        $state = is_saas() ? 'cloud' : (setting('login.email.enable') ? 'email' : 'qrcode');

        return view('user/login')->assign(compact('from', 'state'));
    }

    public function save(Auth $auth)
    {
        if (is_saas()) {
            $authUrl = $this->app->make(Cloud::class)->getAuthorizeUrl();
            return redirect($authUrl);
        } else {
            $this->validate([
                'username|用户名或邮箱' => 'require',
                'password|密码'         => 'require',
            ]);

            $credentials = $this->request->only(['username', 'password']);

            if ($auth->attempt($credentials, $this->request->has('remember'))) {
                return redirect('/-')->restore();
            }

            throw new ValidateException(['password' => '用户名或密码错误']);
        }
    }

    public function cloud(Cloud $cloud, Auth $auth, $code)
    {
        $user = $cloud->login($code);

        $auth->login($user);

        return redirect('/-')->restore();
    }

    public function qrcode(Client $client)
    {
        $res = $client->passport()->login('知识管理');

        return json($res);
    }

    public function check(Client $client, Auth $auth, $token)
    {
        $user = $client->passport()->user($token);

        if (empty($user)) {
            abort(449);
        }

        $socialUser = \yunwuxin\social\User::make($user, []);
        $socialUser->setChannel('passport');

        $user = User::getBySocialUser($socialUser);

        $auth->login($user);

        return redirect('/-')->restore();
    }
}
