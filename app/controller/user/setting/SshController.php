<?php

namespace app\controller\user\setting;

use app\controller\SpaceController;
use app\model\PublicKey;

class SshController extends SpaceController
{
    public function index()
    {
        $keys = PublicKey::where('user_id', $this->user->id)->where('space_id', $this->space->id)->select();

        return view('user/setting/ssh')->assign('keys', $keys);
    }

    public function save()
    {
        $data = $this->validate([
            'title|标题' => 'require',
            'key|公钥'   => ['require', '/\A(ssh|ecdsa)-.*\Z/'],
        ]);

        $data['user_id']  = $this->user->id;
        $data['space_id'] = $this->space->id;

        PublicKey::create($data);

        return redirect('/-/user/setting/ssh');
    }

    public function delete($id)
    {
        $key = PublicKey::where('user_id', $this->user->id)->where('space_id', $this->space->id)->findOrFail($id);

        $key->delete();
    }
}
