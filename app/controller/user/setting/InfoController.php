<?php

namespace app\controller\user\setting;

use app\controller\SpaceController;
use think\file\UploadedFile;
use think\Filesystem;

class InfoController extends SpaceController
{
    public function index()
    {
        if (is_saas()) {
            return view('user/setting/cloud');
        } else {
            return view('user/setting/info');
        }
    }

    public function save(Filesystem $filesystem)
    {
        $data = $this->validate([
            'name'   => '',
            'avatar' => 'image',
        ]);

        if (!empty($data['avatar']) && $data['avatar'] instanceof UploadedFile) {
            $data['avatar'] = $filesystem->disk('uploads')->putFile('avatar', $data['avatar']);
        }

        $this->user->save($data);

        return response('更新成功');
    }
}
