<?php

namespace app\controller\user\setting;

use app\controller\SpaceController;
use app\lib\Otp;
use app\model\User;
use app\notification\EmailCode;

class EmailController extends SpaceController
{
    public function index()
    {
        return view('user/setting/email');
    }

    public function save(Otp $otp)
    {
        $data = $this->validate([
            'email|新邮箱'  => 'require|email|unique:' . User::class,
            'code|验证码'   => [
                'require',
                '\d{6}',
                function ($code, $data) use ($otp) {
                    if ($otp->verify($data['email'], $code)) {
                        return true;
                    }

                    return '验证码不正确';
                },
            ],
            'password|密码' => [
                function ($password) {
                    if (!$this->user->password || password_verify($password, $this->user->password)) {
                        return true;
                    }
                    return '密码不正确';
                },
            ],
        ]);

        $this->user->save([
            'email' => $data['email'],
        ]);
    }

    public function code(Otp $otp)
    {
        $data = $this->validate([
            'email|新邮箱' => 'require|email|unique:' . User::class,
        ]);

        $code = $otp->create($data['email']);

        $this->user->mustNotify(new EmailCode($data['email'], $code));

        return response('验证码已发送，请注意查收。', 202);
    }
}
