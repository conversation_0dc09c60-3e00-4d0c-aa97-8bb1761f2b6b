<?php

namespace app\controller\user\setting;

use app\controller\SpaceController;
use think\helper\Str;

class PasswordController extends SpaceController
{
    public function index()
    {
        return view('user/setting/password');
    }

    public function save()
    {
        $data = $this->validate([
            'old_password|旧密码' => [
                function ($password) {
                    if (!$this->user->password || password_verify($password, $this->user->password)) {
                        return true;
                    }
                    return '密码不正确';
                },
            ],
            'password|新密码'     => 'require|length:6,16|confirm',
        ]);

        $this->user->save([
            'password'       => password_hash($data['password'], PASSWORD_DEFAULT),
            'remember_token' => Str::random(60),
        ]);
    }
}
