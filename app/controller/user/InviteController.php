<?php

namespace app\controller\user;

use app\controller\SpaceController;
use app\model\OrganizationInvite;
use think\exception\ValidateException;
use think\Session;
use function view;

class InviteController extends SpaceController
{
    public function index($key)
    {
        $invite = $this->space->owner->invites()->where('key', $key)->findOrFail();

        return view('user/invite')->assign('invite', $invite);
    }

    public function save(Session $session, $key)
    {
        /** @var OrganizationInvite $invite */
        $invite = $this->space->owner->invites()->where('key', $key)->findOrFail();

        if (!$invite->available) {
            throw new ValidateException('该邀请链接已失效');
        }

        if (!$this->space->checkQuota('member')) {
            throw new ValidateException('该空间成员已满，请联系空间管理员');
        }

        if ($this->user) {
            $this->space->owner->addMember($this->user, overwrite: false, invite: $invite);
            return redirect('/-');
        } else {
            $session->set('invite_id', $invite->id);
            return redirect('/-/user/register');
        }
    }
}
