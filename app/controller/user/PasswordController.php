<?php

namespace app\controller\user;

use app\controller\ViewController;
use app\lib\Date;
use app\model\User;
use app\notification\ResetPassword;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\exception\ValidateException;
use think\helper\Str;
use UnexpectedValueException;

class PasswordController extends ViewController
{
    public function index()
    {
        return view('user/password/index');
    }

    public function save()
    {
        $data = $this->validate([
            'email|邮箱' => 'require|email',
        ]);

        $user = User::where($data)->find();
        if (empty($user)) {
            throw new  ValidateException(['email' => '该邮箱不存在']);
        }

        $token = JWT::encode([
            'email' => $user->email,
            'iat'   => Date::now()->getTimestamp(),
            'exp'   => Date::now()->add(30, 'minutes')->getTimestamp(),
            'iss'   => 'password',
        ], config('app.token'), 'HS256');

        $user->notify(new ResetPassword($token));

        return redirect('/-/user/login');
    }

    public function edit()
    {
        return view('user/password/edit');
    }

    public function update()
    {
        $token = $this->request->param('token');
        try {
            $payload = JWT::decode($token, new Key(config('app.token'), 'HS256'));
            if (empty($payload->email) || empty($payload->iss) || $payload->iss != 'password') {
                throw new UnexpectedValueException();
            }
            $user = User::where('email', $payload->email)->find();
            if (empty($user)) {
                throw new UnexpectedValueException();
            }

            $data = $this->validate([
                'password|新密码' => 'require|length:6,16|confirm',
            ]);

            $user->save([
                'password'       => password_hash($data['password'], PASSWORD_DEFAULT),
                'remember_token' => Str::random(60),
            ]);
            return redirect('/-/user/login');
        } catch (UnexpectedValueException) {
            return redirect('/-/user/password');
        }
    }
}
