<?php

namespace app\controller\user;

use app\controller\ViewController;
use app\event\Register;
use app\lib\Cloud;
use app\lib\Otp;
use app\model\User;
use app\notification\EmailCode;
use think\App;
use think\Event;
use think\Session;
use think\View;
use yunwuxin\Auth;
use yunwuxin\notification\Sender;

class RegisterController extends ViewController
{
    public function __construct(App $app, Auth $auth, View $view, Session $session)
    {
        parent::__construct($app, $auth, $view);

        if (!setting('login.register') && !$session->has('invite_id')) {
            //注册关闭时，非邀请不能注册
            abort(404);
        }
    }

    public function index()
    {
        if (is_saas()) {
            return redirect(app(Cloud::class)->getAuthorizeUrl());
        }
        return view('user/register');
    }

    public function save(Otp $otp, Event $event, Auth $auth)
    {
        $data = $this->validate([
            'email|邮箱'    => 'require|email|unique:' . User::class,
            'code|验证码'   => [
                'require',
                '\d{6}',
                function ($code, $data) use ($otp) {
                    if ($otp->verify($data['email'], $code)) {
                        return true;
                    }

                    return '验证码不正确';
                },
            ],
            'password|密码' => 'require',
        ]);

        //使用邮箱前缀作为昵称
        $user = User::create([
            'name'     => strstr($data['email'], '@', true),
            'email'    => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
        ]);

        $event->trigger(new Register($user));

        $auth->login($user);

        return redirect('/-')->restore();
    }

    public function code(Otp $otp, Sender $sender)
    {
        $data = $this->validate([
            'email|邮箱' => 'require|email|unique:' . User::class,
        ]);

        $code = $otp->create($data['email']);

        $sender->send(null, new EmailCode($data['email'], $code));

        return response('验证码已发送，请注意查收。', 202);
    }
}
