<?php

namespace app\job;

use app\model\Book;
use think\queue\Job;

class InitBook
{
    protected $book;

    public function __construct(protected \app\lib\book\Creator $creator, $id)
    {
        $this->book = Book::find($id);
    }

    public function fire(Job $job)
    {
        if (empty($this->book) || $this->book->status != 0) {
            $job->delete();
            return;
        }

        $this->book->save(['status' => 2]);
        try {
            //TODO custom transfer 只对本地仓库生效
            $this->creator
                ->original($this->book->original_type)
                ->execute($this->book);
            $this->book->save(['status' => 1]);
        } catch (\Exception $e) {
            trace($e->getMessage(), 'error');
            $this->book->save(['status' => -1]);
        }
        $job->delete();
    }

    public function failed()
    {
        $this->book?->save([
            'status' => -1,
        ]);
    }
}
