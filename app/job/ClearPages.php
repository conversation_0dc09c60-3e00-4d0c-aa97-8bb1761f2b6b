<?php

namespace app\job;

use app\model\Book;
use Exception;
use SplFileInfo;
use Symfony\Component\Finder\Finder;
use think\Filesystem;
use think\queue\Job;

class ClearPages
{
    public function __construct(
        protected Filesystem $filesystem,
    )
    {
    }

    public function fire(Job $job, $payload)
    {
        [$id, $sha] = $payload;

        $book = Book::findOrFail($id);

        if ($book->sha == $sha) {

            $pagesDisk = $this->filesystem->disk('pages');

            $currentDir = $pagesDisk->path($book->hash_path . '/' . $book->sha);

            $modifiedTime = filemtime($currentDir);

            //清理文件夹
            if ($pagesDisk->has($book->hash_path)) {
                $dirs = Finder::create()
                    ->in($pagesDisk->path($book->hash_path))
                    ->sortByModifiedTime()
                    ->filter(function (SplFileInfo $file) use ($modifiedTime) {
                        return $file->getMTime() < $modifiedTime;
                    })
                    ->depth(0)
                    ->directories();

                foreach ($dirs as $dir) {
                    try {
                        $pagesDisk->deleteDir($book->hash_path . '/' . $dir->getFilename());
                    } catch (Exception) {
                        //ignore
                    }
                }
            }

            //gc
            if (random_int(1, 100) <= 10) {
                $book->repo()->run('gc');
            }
        }

        $job->delete();
    }
}
