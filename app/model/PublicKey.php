<?php

namespace app\model;

use think\exception\ValidateException;
use think\Model;

/**
 * Class app\model\PublicKey
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property mixed $fingerprint
 * @property mixed $id
 * @property mixed $key
 * @property mixed $space_id
 * @property mixed $title
 * @property mixed $user_id
 * @property-read \app\model\Space $space
 * @property-read \app\model\User $user
 */
class PublicKey extends Model
{

    static function onBeforeInsert(self $model)
    {
        [, $key] = explode(' ', $model->getAttr('key'), 3);
        $fingerprint = self::insecureKeyFingerprint($key);

        if (self::where('fingerprint', $fingerprint)->find()) {
            throw new ValidateException('该公钥已存在');
        }

        $model->fingerprint = $fingerprint;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    public static function insecureKeyFingerprint($key)
    {
        return join(':', str_split(md5(base64_decode($key)), 2));
    }
}
