<?php

namespace app\model;

use app\lib\Cloud;
use app\lib\Date;
use think\facade\Cache;
use think\Filesystem;
use think\helper\Str;
use think\Model;
use think\Session;
use TopThinkCloud\OpenUser;
use yunwuxin\auth\interfaces\Authorizable;
use yunwuxin\auth\traits\AuthorizableUser;
use yunwuxin\notification\Notifiable;

/**
 * Class app\model\User
 *
 * @property \app\lib\Date $block_time
 * @property int $last_space_id
 * @property mixed $access_level
 * @property mixed $avatar
 * @property mixed $delete_time
 * @property mixed $email
 * @property mixed $id
 * @property mixed $name
 * @property mixed $password
 * @property mixed $remember_token
 * @property string $create_time
 * @property string $update_time
 * @property-read \app\model\Organization[] $orgs
 * @property-read \app\model\Space $space
 * @property-read \app\model\TeamMember|\app\model\BookMember $pivot
 * @property-read \app\model\Team[] $teams
 * @property-read mixed $cloud
 */
class User extends Model implements Authorizable, OpenUser
{
    use AuthorizableUser, Notifiable;

    protected $cloud;

    protected $type = [
        'block_time' => Date::class,
    ];

    protected $hidden = ['password', 'remember_token'];

    public static function onAfterInsert(User $model): void
    {
        //不再创建个人空间
        //$model->space()->save([]);
    }

    protected function getNameAttr($name)
    {
        return $name ?: "用户{$this->id}";
    }

    protected function getAvatarAttr($avatar)
    {
        if (!empty($avatar)) {
            if (!Str::startsWith($avatar, 'http')) {
                $url = $this->invoke(function (Filesystem $filesystem) use ($avatar) {
                    return $filesystem->disk('uploads')->url($avatar);
                });
            } else {
                $url = $avatar;
            }
        } else {
            $url = asset('images/logo.svg');
        }
        if (!Str::startsWith($url, 'http')) {
            return (string) url($url, domain: true);
        }
        return $url;
    }

    public function space()
    {
        return $this->morphOne(Space::class, 'owner');
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class, TeamMember::class);
    }

    public function orgs()
    {
        return $this->belongsToMany(Organization::class, OrganizationMember::class, 'org_id', 'user_id');
    }

    public function getIdentifier()
    {
        return $this->id;
    }

    public function getRoles()
    {
        return $this->access_level;
    }

    public function setLastSpace(Space $space)
    {
        if ($space->id != $this->last_space_id) {
            $this->save([
                'last_space_id' => $space->id,
            ]);
        }
    }

    public function getLastSpace()
    {
        if ($this->last_space_id) {
            $space = Space::find($this->last_space_id);
            if ($space && $space->isOrg() && $space->owner->hasMember($this, Member::GUEST)) {
                return $space;
            }
        }

        if ($this->space) {
            return $this->space;
        }

        foreach ($this->orgs as $org) {
            //显示第一个空间
            if ($org->space) {
                return $org->space;
            }
        }

        return null;
    }

    public function getCloudToken()
    {
        return Cache::remember("oauth-token-{$this->id}", function () {
            return OauthToken::where('channel', 'topthink')->where('user_id', $this->id)->value('token');
        }, 3600);
    }

    public function getCloudAttr()
    {
        return $this->invoke(function (Session $session, Cloud $cloud) {
            if ($session->has('cloud')) {
                return $session->get('cloud', []);
            } else {
                $info = $cloud->getUserInfo($this);

                $info['notification_script'] = $cloud->getNotifyScript($this);

                $session->set('cloud', $info);

                return $info;
            }
        });
    }

    public static function getBySocialUser(\yunwuxin\social\User $socialUser)
    {
        $token = OauthToken::where('channel', $socialUser->getChannel())->where('openid', $socialUser->getId())->find();

        if (empty($token)) {
            $data = [
                'name'         => $socialUser['nickname'],
                'email'        => $socialUser['email'] ?? '',
                'avatar'       => $socialUser['avatar'],
                'access_level' => 'regular',
            ];

            if (!User::count()) {
                //第一个用户为管理员
                $data['access_level'] = 'admin';
            }

            //创建用户
            $user = User::create($data);
            OauthToken::create([
                'channel' => $socialUser->getChannel(),
                'openid'  => $socialUser->getId(),
                'user_id' => $user->id,
            ]);
        } else {
            $user = $token->user;
        }

        if ($socialUser['channel'] == 'topthink') {
            $user->save([
                'access_level' => $socialUser->getRaw('is_admin') ? 'admin' : 'regular',
            ]);
        }

        return $user;
    }

    public function getOpenid()
    {
        $token = OauthToken::where('channel', 'topthink')->where('user_id', $this->id)->find();

        if ($token) {
            return $token->openid;
        }
        return null;
    }
}
