<?php

namespace app\model;

use think\helper\Str;

/**
 * Class app\model\BookMember
 *
 * @property \app\lib\Date $create_time
 * @property mixed $access_level
 * @property mixed $book_id
 * @property mixed $member_id
 * @property mixed $member_type
 * @property-read \app\model\User|\app\model\Team $info
 * @property-read mixed $avatar
 * @property-read mixed $id
 * @property-read mixed $name
 */
class BookMember extends Member
{

    protected function getIdAttr()
    {
        return Str::lower(class_basename($this->member_type)) . ':' . $this->member_id;
    }

    /**
     * @param User|Team|string $member
     * @return bool
     */
    public function is($member)
    {
        if (is_string($member)) {
            return $this->member_type == $member;
        }
        return $this->member_type == get_class($member) && $this->member_id == $member->id;
    }

    public function info()
    {
        return $this->morphTo('member');
    }

    protected function getNameAttr()
    {
        return $this->info->getAttr('name');
    }

    protected function getAvatarAttr()
    {
        return $this->info->avatar;
    }
}
