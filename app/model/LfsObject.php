<?php

namespace app\model;

use Exception;
use think\facade\Db;
use think\Model;

/**
 * Class app\model\LfsObject
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property mixed $id
 * @property mixed $oid
 * @property mixed $size
 * @property string $mime_type
 * @property-read \app\model\Book[] $books
 * @property-read mixed $filename
 */
class LfsObject extends Model
{
    protected function getFilenameAttr()
    {
        return substr($this->oid, 0, 2) . '/' . substr($this->oid, 2, 2) . "/{$this->oid}";
    }

    public function books()
    {
        return $this->belongsToMany(Book::class, 'lfs_object_book');
    }

    public static function findOrCreate($data)
    {
        try {
            return Db::transaction(function () use ($data) {
                $object = LfsObject::where($data)->lock(true)->find();

                if (!$object) {
                    $object = LfsObject::create($data);
                }

                return $object;
            });
        } catch (Exception $e) {
            if (str_contains($e->getMessage(), '1062 Duplicate entry')) {
                return LfsObject::where($data)->findOrFail();
            }
            throw $e;
        }
    }
}
