<?php

namespace app\model;

use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\Model;

/**
 * Class app\model\AccessToken
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $last_time
 * @property int $accessible_id
 * @property int $id
 * @property string $accessible_type
 * @property string $name
 * @property string $scopes
 * @property string $token
 * @property-read \app\model\Book|\app\model\Space $accessible
 * @property-read mixed $scopes_name
 */
class AccessToken extends Model
{
    const SCOPE_NAME = [
        'read_book'        => '文档阅读',
        'read_repository'  => '文档数据(读取)',
        'write_repository' => '文档数据(读写)',
        'api'              => '接口调用',
    ];

    protected $type = [
        'create_time' => Date::class,
        'expire_time' => Date::class,
        'last_time'   => Date::class,
    ];

    protected $hidden = ['accessible_id', 'accessible_type', 'token'];

    protected $autoWriteTimestamp = false;

    public function accessible()
    {
        return $this->morphTo();
    }

    public function hasScope($scope)
    {
        return in_array($scope, explode(',', $this->scopes));
    }

    public function isExpired()
    {
        return !empty($this->expire_time) && $this->expire_time->lt(Date::now());
    }

    /**
     * @param \app\model\Book|\app\model\Space $obj
     * @return bool
     */
    public function isAvailableFor($obj)
    {
        $accessible = $this->accessible;
        switch (true) {
            case $obj instanceof Book:
                if ($accessible instanceof Space) {
                    return $obj->space_id == $accessible->id;
                } else {
                    return $accessible->id == $obj->id;
                }
            case $obj instanceof Space:
                return $accessible instanceof Space && $accessible->id == $obj->id;
            case is_null($obj):
                return true;
        }
        return false;
    }

    protected function getScopesNameAttr()
    {
        return implode(',', array_map(function ($scope) {
            return self::SCOPE_NAME[$scope] ?? $scope;
        }, explode(',', $this->scopes)));
    }

    /**
     * @param string $token
     * @param string $type
     * @param $scope
     */
    public static function getByToken($token, $type = null, $scope = null)
    {
        if (!Uuid::isValid($token)) {
            return false;
        }

        $query = AccessToken::where('token', $token);

        if ($type) {
            $query->where('accessible_type', $type);
        }

        $accessToken = $query->find();

        if (empty($accessToken) || $accessToken->isExpired()) {
            return null;
        }

        if ($scope && !$accessToken->hasScope($scope)) {
            return null;
        }

        $accessToken->save([
            'last_time' => Date::now(),
        ]);

        return $accessToken;
    }

}
