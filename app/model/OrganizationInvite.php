<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\OrganizationInvite
 *
 * @property \app\lib\Date $create_time
 * @property mixed $id
 * @property mixed $key
 * @property mixed $org_id
 * @property mixed $status
 * @property mixed $user_id
 * @property-read \app\model\Organization $owner
 * @property-read \app\model\User $user
 * @property-read mixed $available
 * @property-read mixed $status_text
 * @property-read mixed $url
 */
class OrganizationInvite extends Model
{

    protected $append = ['url'];

    protected function getUrlAttr()
    {
        return (string) url('/-/user/invite', ['key' => $this->getAttr('key')], domain: true);
    }

    protected function getAvailableAttr()
    {
        return !($this->status == 0 || $this->create_time->lt(Date::now()->sub('72 hours')));
    }

    protected function getStatusTextAttr()
    {
        return $this->available ? '有效' : '失效';
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function owner()
    {
        return $this->belongsTo(Organization::class, 'org_id');
    }
}
