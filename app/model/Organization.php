<?php

namespace app\model;

use app\lib\Date;
use app\lib\StaticData;
use think\db\Query;
use think\Filesystem;
use think\Model;

/**
 * Class app\model\Organization
 *
 * @property \app\model\Book|null|string $index
 * @property bool $powered_by
 * @property bool $sso
 * @property int $weapp_id
 * @property mixed $id
 * @property mixed $logo
 * @property mixed $name
 * @property string $create_time
 * @property string $idp
 * @property string $update_time
 * @property-read \app\model\Book|null $default_book
 * @property-read \app\model\OrganizationInvite[] $invites
 * @property-read \app\model\Space $space
 * @property-read \app\model\Team[] $teams
 * @property-read \app\model\User[] $members
 * @property-read \app\model\User[]|\think\model\Collection $masters
 * @property-read \app\model\User[]|\think\model\Collection $owners
 * @method static \think\db\Query spaceExist()
 */
class Organization extends Model
{
    use StaticData;

    public static function onAfterInsert(Organization $model): void
    {
        if (is_saas()) {
            $model->space()->save([
                'plan'        => 'trial',
                'expire_time' => Date::now()->addDays(15),
            ]);
        } else {
            $model->space()->save([
                'plan'        => 'enterprise',
                'expire_time' => null,
            ]);
        }
    }

    protected function getLogoAttr($logo)
    {
        if (!empty($logo)) {
            return $this->invoke(function (Filesystem $filesystem) use ($logo) {
                return $filesystem->disk('uploads')->url($logo);
            });
        }
        return asset('images/logo.svg');
    }

    protected function setIdpAttr($data)
    {
        return base64_encode(gzdeflate($data));
    }

    protected function getIdpAttr($data)
    {
        if ($data) {
            return gzinflate(base64_decode($data));
        }
        return null;
    }

    public function space()
    {
        return $this->morphOne(Space::class, 'owner');
    }

    public function members()
    {
        return $this->belongsToMany(User::class, OrganizationMember::class, 'user_id', 'org_id');
    }

    protected function getIndexAttr($index)
    {
        return $this->getStaticData(function () use ($index) {
            if (is_numeric($index)) {
                return $this->space->books()->find($index);
            }
            return $index ?: null;
        });
    }

    protected function getPoweredByAttr($value)
    {
        if (!$this->space->isEnterprisePlan()) {
            return true;
        }
        return !!$value;
    }

    /**
     * @param User $member
     * @return bool
     */
    public function hasMember($member, $accessLevel = null)
    {
        /** @var OrganizationMember|false $pivot */
        $pivot = $this->getStaticData("member-{$member->id}", function () use ($member) {
            return $this->members()->attached($member);
        });

        if (!$pivot) {
            return false;
        }

        $pivotAccessLevel = $pivot->block_time ? Member::BLOCKER : $pivot->access_level;

        return $accessLevel == null || $pivotAccessLevel >= $accessLevel;
    }

    /**
     * @param User|integer $member
     */
    public function addMember($member, $accessLevel = Member::READER, $overwrite = true, OrganizationInvite $invite = null)
    {
        /** @var OrganizationMember|null $pivot */
        $pivot = $this->members()->attached($member) ?: null;

        if ($overwrite || !$pivot) {
            $this->members()->attach($member, [
                'access_level' => $accessLevel,
                'create_time'  => $pivot?->create_time,
                'block_time'   => $pivot?->block_time,
                'invite_id'    => $invite?->id ?: $pivot?->invite_id,
            ]);
        }
    }

    /**
     * 管理员
     * @return void
     */
    protected function getMastersAttr()
    {
        return $this->members()->wherePivot('access_level', '>=', Member::MASTER)->select();
    }

    /**
     * 创始人
     * @return void
     */
    protected function getOwnersAttr()
    {
        return $this->members()->wherePivot('access_level', Member::OWNER)->select();
    }

    public function teams()
    {
        return $this->hasMany(Team::class, 'org_id');
    }

    public function invites()
    {
        return $this->hasMany(OrganizationInvite::class, 'org_id');
    }

    /**
     * 检查空间是否已删除
     * @param Query $query
     * @return void
     */
    public function scopeSpaceExist(Query $query)
    {
        $query->whereExists(function (Query $query) {
            $query->table('space')
                ->whereRaw('organization.id=space.owner_id')
                ->where('space.owner_type', Organization::class)
                ->where(function (Query $query) {
                    $query->whereNull('space.delete_time', 'OR');
                    $query->whereOr('space.delete_time', '>', Date::now()->subDay());
                });
        });
    }
}
