<?php

namespace app\model;

use think\Filesystem;
use think\helper\Str;
use think\Model;

/**
 * Class app\model\Team
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property mixed $id
 * @property mixed $name
 * @property mixed $org_id
 * @property-read \app\model\User[] $members
 * @property-read mixed $avatar
 */
class Team extends Model
{
    public function members()
    {
        return $this->belongsToMany(User::class, TeamMember::class);
    }

    protected function getAvatarAttr()
    {
        return asset('images/team.svg');
    }
}
