<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\OauthToken
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property mixed $channel
 * @property mixed $id
 * @property mixed $openid
 * @property mixed $token
 * @property mixed $user_id
 * @property-read \app\model\User $user
 */
class OauthToken extends Model
{

    protected $hidden = ['token'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
