<?php

namespace app\model;

use think\model\Pivot;

abstract class Member extends Pivot
{
    const OWNER   = 60;
    const MASTER  = 50;
    const CREATOR = 40;
    const WRITER  = 30;
    const READER  = 20;
    const GUEST   = 10;
    const BLOCKER = 0;

    protected function getRoleAttr()
    {
        $accessLevel = (int) $this->getAttr('access_level');

        return match ($accessLevel) {
            self::OWNER => '创始人',
            self::MASTER => '管理员',
            self::CREATOR => '创作者',
            self::WRITER => '写作者',
            self::READER => '阅读者',
            default => $accessLevel,
        };
    }
}
