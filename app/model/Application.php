<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\Application
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property mixed $client_id
 * @property mixed $client_secret
 * @property mixed $id
 * @property mixed $is_confidential
 * @property mixed $name
 * @property mixed $redirect_uri
 * @property mixed $scopes
 * @property mixed $space_id
 */
class Application extends Model
{

    public static function validate($identifier, $secret, $grantType): bool
    {
        return !!static::where('client_id', $identifier)->where('client_secret', $secret)->find();
    }

    protected function setScopesAttr($scopes)
    {
        return join(',', $scopes);
    }

    protected function getScopesAttr($scopes)
    {
        if (empty($scopes)) {
            return [];
        }
        return explode(',', $scopes);
    }
}
