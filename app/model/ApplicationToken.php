<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\AccessToken
 *
 * @property \DateTimeImmutable $expire_time
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $space_id
 * @property mixed $client_id
 * @property mixed $id
 * @property mixed $scopes
 * @property mixed $user_id
 * @property-read \app\model\Application $application
 */
class ApplicationToken extends Model
{

    protected $type = [
        'expire_time' => Date::class,
    ];

    public function application()
    {
        return $this->belongsTo(Application::class, 'client_id', 'client_id');
    }

    public static function revoke($id)
    {
        static::destroy($id);
    }

    public static function isRevoked($id): bool
    {
        return empty(static::find($id));
    }

    protected function setScopesAttr($scopes)
    {
        return join(',', $scopes);
    }

    protected function getScopesAttr($scopes)
    {
        if (empty($scopes)) {
            return [];
        }
        return explode(',', $scopes);
    }

    public function can($scope)
    {
        return in_array($scope, $this->scopes + $this->application->scopes);
    }

}
