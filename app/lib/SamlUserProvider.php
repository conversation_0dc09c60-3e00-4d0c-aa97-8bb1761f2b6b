<?php

namespace app\lib;

use yunwuxin\auth\interfaces\StatefulProvider;

class SamlUserProvider implements StatefulProvider
{

    public function retrieveByCredentials($credentials)
    {

    }

    /**
     * @param \app\lib\SamlUser $user
     * @return mixed|void
     */
    public function getId($user)
    {
        return json_encode($user->toArray());
    }

    public function getRememberToken($user)
    {

    }

    public function setRememberToken($user, $token)
    {

    }

    public function retrieveById($id)
    {
        $data = json_decode($id, true);
        if (is_array($data)) {
            return new SamlUser($data);
        }
        return null;
    }

    public function retrieveByToken($id, $token)
    {

    }
}
