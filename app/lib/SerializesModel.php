<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2017 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib;

use think\Model;
use function method_exists;

trait SerializesModel
{
    public function __serialize()
    {
        $vars = get_object_vars($this);

        $data = [];
        foreach ($vars as $name => $var) {
            if ($var instanceof Model) {
                $pk          = $var->getPk();
                $data[$name] = [get_class($var), $var->$pk];
            } else {
                $data[$name] = $var;
            }
        }

        return $data;
    }

    public function __unserialize($data)
    {
        foreach ($data as $name => $var) {
            if (is_array($var) && isset($var[0], $var[1]) && is_subclass_of($var[0], Model::class)) {
                [$model, $id] = $var;

                if (method_exists($model, 'withTrashed')) {
                    $this->$name = $model::withTrashed()->find($id);
                } else {
                    $this->$name = $model::get($id);
                }
            } else {
                $this->$name = $var;
            }
        }
    }
}
