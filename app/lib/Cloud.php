<?php

namespace app\lib;

use app\model\OauthToken;
use app\model\User;
use RuntimeException;
use think\App;
use think\Cache;
use TopThinkCloud\AccessToken;
use TopThinkCloud\Client;
use TopThinkCloud\Exception\ErrorException;
use TopThinkCloud\OAuth;

class Cloud
{

    public function __construct(protected App $app, protected Cache $cache)
    {

    }

    public function isAvailable()
    {
        return is_saas();
    }

    public function getAuthorizeUrl()
    {
        return $this->getOAuth()->getAuthorizeUrl(false);
    }

    public function createCharge($params)
    {
        return $this->getClient()->charge()->create($params);
    }

    public function verifyChargeNotify($data)
    {
        return $this->getClient(false)->charge()->verify($data);
    }

    public function getWeapp($id)
    {
        return $this->getClient()->weapp()->show($id);
    }

    public function getWeappQrcode($id, $page = null, $scene = null)
    {
        return $this->getClient()->weapp()->qrcode($id, $page, $scene);
    }

    public function updateWeapp($id, $data)
    {
        return $this->getClient()->weapp()->update($id, $data);
    }

    /**
     * @param $data
     */
    public function createWeapp($data)
    {
        return $this->getClient()->weapp()->save($data);
    }

    public function getNotifyScript(User $user)
    {
        $token = $user->getCloudToken();
        return $this->getClient(false)->notification()->getScript($token);
    }

    public function login($code)
    {
        try {
            $accessToken = $this->getOAuth()->getAccessToken('authorization_code', [
                'code' => $code,
            ]);
        } catch (ErrorException) {
            abort(401, '认证信息已过期');
        }

        $info = $this->getUserInfo($accessToken);

        $token = OauthToken::where('channel', 'topthink')->where('openid', $info['id'])->find();

        $data = [
            'name'         => $info['name'],
            'email'        => $info['email'] ?? '',
            'avatar'       => $info['avatar'],
            'access_level' => $info['is_admin'] ? 'admin' : 'regular',
        ];

        if (empty($token)) {
            //创建用户
            $user = User::create($data);
            OauthToken::create([
                'channel' => 'topthink',
                'openid'  => $info['id'],
                'user_id' => $user->id,
                'token'   => (string) $accessToken,
            ]);
        } else {
            $token->save([
                'token' => (string) $accessToken,
            ]);
            $user = $token->user;
            //更新用户信息
            $user->save($data);
            $this->cache->delete("oauth-token-{$user->id}");
        }

        return $user;
    }

    /**
     * @param User|AccessToken $user
     */
    public function getUserInfo($user)
    {
        return $this->getClient($user)->currentUser()->info();
    }

    public function getUserCerts($user, $params = [])
    {
        return $this->getClient($user)->currentUser()->cert()->all($params);
    }

    public function getUserCert($user, $id)
    {
        return $this->getClient($user)->currentUser()->cert()->show($id);
    }

    public function getClientToken()
    {
        return $this->getClient(false)->getClientToken();
    }

    protected function getOAuth()
    {
        if (!$this->isAvailable()) {
            throw new RuntimeException('cloud is not available');
        }
        return $this->app->make(OAuth::class);
    }

    /**
     * @param User|null|AccessToken|false $user
     * @return Client
     */
    protected function getClient($user = null)
    {
        if (!$this->isAvailable()) {
            throw new RuntimeException('cloud is not available');
        }

        $client = $this->app->make(Client::class);

        if ($user !== false) {
            if ($user) {
                if ($user instanceof AccessToken) {
                    $token = $user;
                } else {
                    $token = $user->getCloudToken();
                }
            } else {
                $token = null;
            }

            $client->authenticate($token);
        }

        return $client;
    }
}
