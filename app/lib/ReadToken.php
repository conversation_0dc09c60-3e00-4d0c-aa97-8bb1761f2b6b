<?php

namespace app\lib;

use app\model\AccessToken;
use app\model\Book;
use app\Request;
use think\exception\HttpResponseException;
use think\Session;
use yunwuxin\Auth;

class ReadToken
{
    public function __construct(protected Session $session)
    {
    }

    public function check($token, Book $book)
    {
        $accessToken = AccessToken::getByToken($token, scope: 'read_book');

        if ($accessToken && $accessToken->isAvailableFor($book)) {
            $this->session->set("read_token_{$book->id}", $accessToken->id);
            return true;
        }
        return false;
    }

    public function associate(Book $book, AccessToken $accessToken)
    {
        $this->session->set("read_token_{$book->id}", $accessToken->id);
    }

    public function associated(Book $book, Request $request)
    {
        if ($book->token_read) {
            if ($request->has('token')) {
                $token = $request->param('token');
                if ($this->check($token, $book)) {
                    return true;
                }
            }

            if ($this->session->has("read_token_{$book->id}")) {
                return true;
            }

            $view = view('read/token')->assign('book', $book)
                ->assign('is_parked', $request->isMode(Request::DOMAIN_PARKED));

            throw new HttpResponseException($view);
        }

        return false;
    }
}
