<?php

namespace app\lib\book\creator;

use app\model\Book;
use <PERSON>uz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use phpseclib3\Crypt\RSA;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Filesystem\Path;
use think\exception\ValidateException;
use think\facade\Log;
use topthink\git\Repository;

class Kancloud extends Git
{
    protected $client;

    public function __construct(protected Filesystem $fs)
    {
        parent::__construct($fs);

        $this->client = new Client([
            'base_uri'    => 'https://www.kancloud.cn',
            'http_errors' => true,
            'headers'     => [
                'Accept' => 'application/json',
            ],
        ]);
    }

    protected function request($method, $uri, $options = [])
    {
        try {
            $response = $this->client->request($method, $uri, $options);

            return json_decode((string) $response->getBody(), true);
        } catch (RequestException $e) {
            if ($e->getCode() == 401) {
                throw new ValidateException('无效令牌');
            }
            throw $e;
        }
    }

    public function getSpaces($token)
    {
        $result = $this->request('get', '/api/user/space', [
            'headers' => [
                'Authorization' => "Bearer {$token}",
            ],
        ]);

        return json($result);
    }

    public function getBooks($token, $space)
    {
        $result = $this->request('get', '/api/user/book/writable', [
            'headers' => [
                'Authorization' => "Bearer {$token}",
            ],
            'query'   => [
                'namespace' => $space,
            ],
        ]);

        return json($result);
    }

    public function createBooks($token, $books = [])
    {
        if (empty($books)) {
            throw new ValidateException('请选择要导入的文档');
        }

        if (count($books) > 10) {
            throw new ValidateException('单次最多导入10个文档');
        }

        $importing = $this->space->books()->where('original_type', 'kancloud')->where('status', 0)->count();

        if ($importing > 0) {
            throw new ValidateException('有正在导入的文档，请等待完成后再导入新的文档。');
        }

        //创建文档
        foreach ($books as $book) {
            //创建文档
            $visibility = $book['visibility_level'];
            if ($visibility == Book::LEVEL_PROTECTED && !$this->space->isOrg()) {
                $visibility = Book::LEVEL_PRIVATE;
            }

            $this->space->createBook($this->user, [
                'name'             => $book['name'],
                'visibility_level' => $visibility,
                'original_type'    => 'kancloud',
                'original_path'    => "{$book['path']}|{$token}",
            ]);
        }

        return redirect('/-/workspace');
    }

    protected function createRepo(Book $book, $dir)
    {
        [$url, $token] = explode('|', $book->original_path);

        //创建公钥
        $private = RSA::createKey();
        $public  = $private->getPublicKey();

        $result = $this->request('post', '/api/user/key', [
            'headers'     => [
                'Authorization' => "Bearer {$token}",
            ],
            'form_params' => [
                'title' => '迁移公钥',
                'key'   => $public->toString('OpenSSH'),
            ],
        ]);

        //创建私钥
        $keyPath = Path::join(runtime_path(), "keys/kancloud/key_{$result['id']}");

        $this->fs->dumpFile($keyPath, (string) $private);
        $this->fs->chmod($keyPath, 0600);
        try {
            //clone 仓库
            $this->cloneRepo($dir, $url, true, ['environment' => [
                'GIT_SSH_COMMAND' => "ssh -i {$keyPath}",
            ]]);

            return Repository::init($dir, false);
        } finally {
            //清理公钥
            $this->request('delete', "/api/user/key/{$result['id']}", [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                ],
            ]);
            $this->fs->remove($keyPath);
        }
    }

    protected function initRepo(Book $book, Repository $repo)
    {
        parent::initRepo($book, $repo);

        //处理图片到lfs
        $repo->run('lfs', ['track', "images/*.png"]);
        $repo->run('lfs', ['track', "images/*.jpg"]);
        $repo->run('lfs', ['track', "images/*.gif"]);

        //样式文件
        if ($this->fs->exists(Path::join($repo->getWorkingDir(), 'style/website.css'))) {
            $this->fs->mkdir(Path::join($repo->getWorkingDir(), '.topwrite'));

            $this->fs->rename(
                Path::join($repo->getWorkingDir(), 'style/website.css'),
                Path::join($repo->getWorkingDir(), '.topwrite/style.css')
            );
        }

        //处理SUMMARY.md,替换文件名中的空白符
        $summaryPath = Path::join($repo->getWorkingDir(), 'SUMMARY.md');
        if ($this->fs->exists($summaryPath)) {
            $summary = file_get_contents($summaryPath);

            $summary = preg_replace("/\((\S+?(\s+)\S*?\.md)\)/m", '(<${1}>)', $summary);

            file_put_contents($summaryPath, $summary);
        }
    }
}
