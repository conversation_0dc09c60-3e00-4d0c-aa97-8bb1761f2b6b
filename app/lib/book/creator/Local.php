<?php

namespace app\lib\book\creator;

use app\model\Book;
use topthink\git\Repository;

abstract class Local extends Git
{
    /** @var Book */
    protected $original;

    abstract function resolveOriginal(Book $book);

    protected function createRepo(Book $book, $dir)
    {
        $this->original = $this->resolveOriginal($book);

        $this->cloneRepo($dir, $this->original->repo_path, true, [
            'environment' => [
                'GIT_LFS_SKIP_SMUDGE' => '1',
            ],
        ]);

        $repo = Repository::init($dir, false);

        $repo->run('config', ['lfs.allowincompletepush', 'true']);

        return $repo;
    }

    protected function pushRepo(Book $book, Repository $repo)
    {
        $remote = "{$book->hash_id}/{$this->original->hash_id}";

        //remote
        $repo->getRemote()->add($remote, $book->repo_path);
        $repo->run('push', [$remote, 'master', '-f']);
    }
}
