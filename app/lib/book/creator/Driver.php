<?php

namespace app\lib\book\creator;

use app\lib\book\Config;
use app\model\Book;
use app\model\Space;
use app\model\User;
use Exception;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Filesystem\Path;
use topthink\git\Repository;

abstract class Driver
{
    /** @var Space */
    protected $space;

    /** @var User */
    protected $user;

    public function __construct(protected Filesystem $fs)
    {
    }

    /**
     * @param Space $space
     */
    public function setSpace(Space $space): void
    {
        $this->space = $space;
    }

    /**
     * @param User $user
     */
    public function setUser(User $user): void
    {
        $this->user = $user;
    }

    protected function initRepo(Book $book, Repository $repo)
    {
        //临时使用仓库hooks
        $repo->run('config', ['core.hooksPath', '.git/hooks']);
        //lfs
        $repo->run('lfs', ['install', '--local', '--force']);
    }

    /**
     * @param Book $book
     * @param $dir
     * @return Repository
     */
    abstract protected function createRepo(Book $book, $dir);

    protected function pushRepo(Book $book, Repository $repo)
    {
        //remote
        $repo->getRemote()->add($book->hash_id, $book->repo_path);
        $repo->push('master', $book->hash_id, true);
    }

    public function execute(Book $book)
    {
        //创建初始化版本库
        if ($this->fs->exists($book->repo_path)) {
            $this->fs->remove($book->repo_path);
        }
        Repository::init($book->repo_path);

        $dir = runtime_path('book/' . uniqid());
        $this->fs->mkdir($dir);

        try {
            $repo = $this->createRepo($book, $dir);

            $this->initRepo($book, $repo);
            //写入文档名称
            //TODO 子目录
            $config = Config::fromFile(Path::join($dir, 'book.json'));
            $config->setValue('title', $book->name);
            file_put_contents(Path::join($dir, 'book.json'), (string) $config);

            //推送仓库
            $repo->add();
            if ($repo->isDirty()) {
                $repo->run('commit', ['-m', 'init']);
            }

            $this->pushRepo($book, $repo);
        } finally {
            try {
                $this->fs->remove($dir);
            } catch (Exception) {

            }
        }
    }

}
