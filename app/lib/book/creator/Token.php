<?php

namespace app\lib\book\creator;

use app\model\AccessToken;
use app\model\Book;
use InvalidArgumentException;

class Token extends Local
{
    public function resolveOriginal(Book $book)
    {
        $accessToken = AccessToken::getByToken($book->original_path, Book::class, 'read_repository');
        if (empty($accessToken)) {
            throw new InvalidArgumentException('无效的文档令牌');
        }

        return $accessToken->accessible;
    }
}
