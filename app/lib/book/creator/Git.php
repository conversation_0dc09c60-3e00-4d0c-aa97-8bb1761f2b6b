<?php

namespace app\lib\book\creator;

use app\model\Book;
use Symfony\Component\Filesystem\Path;
use topthink\git\Repository;

class Git extends Driver
{
    protected function cloneRepo($dir, $url, $clean = true, $options = [])
    {
        $args = ['--no-tags', '--single-branch', '--shallow-submodules'];
        if ($clean) {
            $args[] = '--depth=1';
        }

        $repo = Repository::clone($dir, $url, $args, options: $options);

        if ($clean) {
            //删除历史
            $this->fs->remove(Path::join($dir, '.git'));
        }

        return $repo;
    }

    protected function createRepo(Book $book, $dir)
    {
        $this->cloneRepo($dir, $book->original_path);
        return Repository::init($dir, false);
    }

}
