<?php

namespace app\lib\book;

use ArrayAccess;
use JsonSerializable;

class SummaryArticle implements JsonSerializable, ArrayAccess
{

    protected function __construct(
        public $level,
        public $title,
        public $ref,
        public $path,
        public $children = [],
        public $metadata = [],
    )
    {
    }

    public static function create($article, $level)
    {
        $index    = 0;
        $children = array_map(function ($article) use ($level, &$index) {
            $index++;
            return SummaryArticle::create($article, "{$level}.{$index}");
        }, $article['children'] ?? []);

        return new self($level, $article['title'], $article['ref'], $article['path'], $children, $article['metadata'] ?? []);
    }

    public static function findArticle(SummaryArticle|SummaryPart $base, $iter)
    {
        $articles = $base->getArticles();
        return array_reduce($articles, function ($result, $article) use ($iter) {
            if ($result) return $result;

            if ($iter($article)) {
                return $article;
            }

            return self::findArticle($article, $iter);
        }, null);
    }

    public function getArticles()
    {
        return $this->children;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'title'    => $this->title,
            'ref'      => $this->ref,
            'path'     => $this->path,
            'children' => $this->children,
            'metadata' => $this->metadata,
        ];
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->$offset);
    }

    public function offsetGet(mixed $offset): mixed
    {
        return $this->$offset;
    }

    public function offsetSet(mixed $offset, mixed $value): void
    {

    }

    public function offsetUnset(mixed $offset): void
    {

    }
}
