<?php

namespace app\lib\book;

use ArrayAccess;
use JsonSerializable;
use think\helper\Arr;

class SummaryPart implements JsonSerializable, ArrayAccess
{

    protected function __construct(
        public $level,
        public $title,
        public $articles = [],
        public $metadata = [],
    )
    {
    }

    public static function create($part, $level)
    {
        $index    = 0;
        $articles = array_map(function ($article) use ($level, &$index) {
            $index++;
            return SummaryArticle::create($article, "{$level}.{$index}");
        }, $part['articles'] ?? []);

        return new self($level, $part['title'], $articles, $part['metadata'] ?? []);
    }

    public function getMetadata($name, $default = null)
    {
        return Arr::get($this->metadata, $name, $default);
    }

    public function getArticle($articleIter)
    {
        return SummaryArticle::findArticle($this, $articleIter);
    }

    public function getArticles()
    {
        return $this->articles;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'title'    => $this->title,
            'articles' => $this->articles,
            'metadata' => $this->metadata,
        ];
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->$offset);
    }

    public function offsetGet(mixed $offset): mixed
    {
        return $this->$offset;
    }

    public function offsetSet(mixed $offset, mixed $value): void
    {

    }

    public function offsetUnset(mixed $offset): void
    {

    }
}
