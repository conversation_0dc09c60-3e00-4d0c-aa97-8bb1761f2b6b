<?php

namespace app\lib\book;

use ArrayAccess;
use JsonException;
use JsonSerializable;

class Summary implements JsonSerializable, ArrayAccess
{

    /**
     * @param SummaryPart[] $parts
     */
    protected function __construct(public $parts = [])
    {
    }

    public static function create($parts)
    {
        $index = 0;
        return new self(array_map(function ($part) use (&$index) {
            $index++;
            return SummaryPart::create($part, "{$index}");
        }, $parts));
    }

    public static function createFromJson($content)
    {
        if ($content) {
            try {
                $parts = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException) {
            }
        }
        return self::create($parts ?? []);
    }

    /**
     *
     * @param mixed $articleIter
     * @param mixed $partIter
     * @return null|SummaryArticle
     */
    public function getArticle($articleIter, $partIter = null)
    {
        if (is_string($articleIter)) {
            $articleIter = fn($article) => $article->path === $articleIter;
        }

        return array_reduce($this->parts, function ($result, $part) use ($articleIter, $partIter) {
            if ($result) {
                return $result;
            }

            if ($partIter && !$partIter($part)) {
                return null;
            }

            return SummaryArticle::findArticle($part, $articleIter);
        }, null);
    }

    public function getPart($level)
    {
        return $this->parts[$level - 1] ?? null;
    }

    public function getParts()
    {
        return $this->parts;
    }

    public function jsonSerialize(): mixed
    {
        return $this->parts;
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->$offset);
    }

    public function offsetGet(mixed $offset): mixed
    {
        return $this->$offset;
    }

    public function offsetSet(mixed $offset, mixed $value): void
    {

    }

    public function offsetUnset(mixed $offset): void
    {

    }
}
