<?php

namespace app\lib\book;

use ArrayAccess;
use ArrayObject;
use JsonException;
use JsonSerializable;
use think\helper\Arr;

class Config implements JsonSerializable, ArrayAccess
{
    protected $values = [];

    public static function fromFile($path)
    {
        if (file_exists($path)) {
            $content = file_get_contents($path);
            return self::fromString($content);
        }
        return new self();
    }

    public static function fromString($content)
    {
        $config = new self();

        try {
            $values = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
            $config->setValues($values);
        } catch (JsonException) {
        }

        return $config;
    }

    public function toString()
    {
        return json_encode($this->values, JSON_ERROR_NONE | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    public function __toString(): string
    {
        return $this->toString();
    }

    public function getStructure($type, $default)
    {
        return $this->getValue('structure.' . $type, $default);
    }

    public function setValues($values)
    {
        $this->values = $values;
    }

    public function setValue($offset, $value)
    {
        return Arr::set($this->values, $offset, $value);
    }

    public function getValue($offset, $default = null)
    {
        return Arr::get($this->values, $offset, $default);
    }

    public function getValues()
    {
        return $this->values;
    }

    public function jsonSerialize(): mixed
    {
        return new ArrayObject($this->values);
    }

    public function offsetExists($offset): bool
    {
        return array_key_exists($offset, $this->values);
    }

    public function offsetGet($offset): mixed
    {
        return $this->values[$offset];
    }

    public function offsetSet($offset, $value): void
    {
        $this->values[$offset] = $value;
    }

    public function offsetUnset($offset): void
    {
        unset($this->values[$offset]);
    }
}
