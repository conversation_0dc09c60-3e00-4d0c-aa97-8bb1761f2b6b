<?php

namespace app\lib;

use app\exception\CdnException;
use app\model\Domain;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use think\helper\Arr;
use think\helper\Str;

class Cdn
{
    const STATUS_TEXT = [
        1  => '服务中',
        2  => '审核中',
        -1 => '未通过审核',
        0  => '已关闭',
    ];

    public function __construct()
    {

    }

    public function getBucketName(Domain $domain)
    {
        return "k-{$domain->space->id}-{$domain->space->hash_id}";
    }

    public function getService(Domain $domain)
    {
        try {
            $response = $this->getClient()->get('/buckets/info', [
                'query' => [
                    'bucket_name' => $this->getBucketName($domain),
                ],
            ]);

            $result = json_decode((string) $response->getBody(), true);

            switch (true) {
                case in_array($domain->name, Arr::get($result, 'approval_domains', [])):
                    $status = 1;
                    break;
                case in_array($domain->name, Arr::get($result, 'disapproval_domains', [])):
                    $status = -1;
                    break;
                case in_array($domain->name, Arr::get($result, 'approvaling_domains', [])):
                    $status = 2;
                    break;
                default:
                    $status = 0;
                    break;
            }

            if ($status == 1 && !Arr::get($result, 'visible')) {
                $status = 0;
            }

            return [
                'status'  => $status,
                'message' => self::STATUS_TEXT[$status],
                'https'   => in_array($domain->name, Arr::get($result, 'approval_domains_https', [])),
            ];
        } catch (RequestException) {
            return null;
        }
    }

    public function createService(Domain $domain)
    {
        $bucket = $this->getBucketName($domain);

        //创建服务
        try {
            $this->getClient()->put('/buckets', [
                'json' => [
                    'bucket_name'   => $bucket,
                    'type'          => 'ucdn',
                    'business_type' => 'file',
                ],
            ]);
        } catch (RequestException $e) {
            $result = json_decode((string) $e->getResponse()->getBody(), true);
            if (Arr::get($result, 'type') != 'BucketAlreadyExists') {
                throw new CdnException(Arr::get($result, 'message'));
            }
            $this->setBucketVisible($bucket, true);
            try {
                $this->disableServiceHttps($domain);
            } catch (RequestException) {

            }
        }

        try {
            //回源设置
            $this->getClient()->post('/v2/buckets/cdn/source', [
                'json' => [
                    'bucket_name' => $bucket,
                    'cdn'         => [
                        'servers' => [
                            [
                                'host'         => 'c.k.topthink.com',
                                'port'         => 80,
                                'weight'       => 1,
                                'max_fails'    => 3,
                                'fail_timeout' => 30,
                            ],
                        ],
                    ],
                    'source_type' => 'protocol_follow',
                    'domain'      => $domain->name . '.' . config('app.host'),
                ],
            ]);

            //设置参数跟随
            $this->getClient()->post('/security/qs', [
                'json' => [
                    'bucket_name' => $bucket,
                    'args_follow' => 'enable',
                ],
            ]);

            //lfs资源迁移
            $this->updateBucketMirror($bucket);
        } catch (RequestException $e) {
            $result = json_decode((string) $e->getResponse()->getBody(), true);
            throw new CdnException(Arr::get($result, 'message'));
        }

        //检查域名是否已经绑定
        try {
            $response = $this->getClient()->get('/domains/buckets', ['query' => ['domain' => $domain->name]]);

            $result = json_decode((string) $response->getBody(), true);

            if (Arr::get($result, 'result')) {
                $this->deleteBucketDomain(Arr::get($result, 'data.bucket_name'), $domain->name);
            }
        } catch (RequestException) {

        }

        //添加域名
        try {
            $this->getClient()->put('/buckets/domains', [
                'json' => [
                    'bucket_name' => $bucket,
                    'domain'      => $domain->name,
                ],
            ]);
        } catch (RequestException $e) {
            $result = json_decode((string) $e->getResponse()->getBody(), true);
            throw new CdnException(Arr::get($result, 'message'));
        }

        //添加dns
        $this->createDns($domain->space->cname, "{$bucket}.b0.aicdn.com");
    }

    public function deleteService(Domain $domain)
    {
        $bucket = $this->getBucketName($domain);

        $this->deleteBucketDomain($bucket, $domain->name);

        $this->setBucketVisible($bucket, false);

        $this->deleteDns($domain->space->cname);
    }

    public function enableService(Domain $domain)
    {
        $this->setBucketVisible($this->getBucketName($domain), true);
    }

    public function disableService(Domain $domain)
    {
        $this->setBucketVisible($this->getBucketName($domain), false);
    }

    protected function getCertId(Domain $domain)
    {
        $response = $this->getClient()->get('/https/services/manager', ['query' => ['domain' => $domain->name]]);

        $result = json_decode((string) $response->getBody(), true);

        foreach (Arr::get($result, 'result', []) as $cert) {
            if ($cert['https']) {
                return $cert['certificate_id'];
            }
        }

        return null;
    }

    public function enableServiceHttps(Domain $domain, $certificate, $key)
    {
        $newId = $this->createCert($certificate, $key);
        $oldId = $this->getCertId($domain);

        try {
            if (empty($oldId)) {
                $this->getClient()->post('/https/certificate/manager', [
                    'json' => [
                        'certificate_id' => $newId,
                        'domain'         => $domain->name,
                        'https'          => true,
                        'force_https'    => true,
                    ],
                ]);
            } else {
                $this->getClient()->post('/https/migrate/domain', [
                    'json' => [
                        'domain_name' => $domain->name,
                        'crt_id'      => $newId,
                    ],
                ]);
            }
        } catch (RequestException $e) {
            $result = json_decode((string) $e->getResponse()->getBody(), true);
            throw new CdnException(Arr::get($result, 'message'));
        }
    }

    public function disableServiceHttps(Domain $domain)
    {
        $id = $this->getCertId($domain);

        if ($id) {
            try {
                $this->getClient()->post('/https/certificate/manager', [
                    'json' => [
                        'certificate_id' => $id,
                        'domain'         => $domain->name,
                        'https'          => false,
                    ],
                ]);
            } catch (RequestException $e) {
                $result = json_decode((string) $e->getResponse()->getBody(), true);
                throw new CdnException(Arr::get($result, 'message'));
            }
        }
    }

    public function updateBucketMirror(string $name)
    {
        $this->getClient()->post('/buckets/cdn/mirror', [
            'json' => [
                'bucket_name'   => $name,
                'mirror'        => 'enable',
                "url_patterns"  => [
                    "/lfs/*",
                ],
                'mirror_bucket' => 'lfs-storage',
            ],
        ]);
    }

    protected function deleteBucketDomain(string $name, string $domain)
    {
        try {
            $this->getClient()->delete('/buckets/domains', [
                'query' => [
                    'bucket_name' => $name,
                    'domain'      => $domain,
                ],
            ]);
        } catch (RequestException) {
        }
    }

    protected function setBucketVisible($name, $visible)
    {
        try {
            $this->getClient()->post("/buckets/visible", [
                'json' => [
                    'bucket_name' => $name,
                    'visible'     => $visible,
                ],
            ]);
        } catch (RequestException) {

        }
    }

    public function queryFlow(Domain $domain)
    {
        $start = Date::now()->startOfMonth();
        $end   = Date::now()->endOfMonth();

        $response = $this->getClient()->get('/v2/statistics', [
            'query' => [
                'bucket_name' => $this->getBucketName($domain),
                'start_time'  => $start->toDateTimeString(),
                'end_time'    => $end->toDateTimeString(),
            ],
        ]);

        $result = json_decode((string) $response->getBody(), true);

        return array_sum(array_map(function ($data) {
            return Arr::get($data, 'bytes', 0);
        }, Arr::get($result, 'data')));
    }

    protected function createCert($certificate, $key)
    {
        try {
            $response = $this->getClient()->post('/https/certificate', [
                'json' => [
                    'certificate' => $certificate,
                    'private_key' => $key,
                ],
            ]);

            $result = json_decode((string) $response->getBody(), true);

            return Arr::get($result, 'result.certificate_id');
        } catch (RequestException $e) {
            $result = json_decode((string) $e->getResponse()->getBody(), true);
            throw new CdnException(Arr::get($result, 'message'));
        }
    }

    protected function getClient()
    {
        $token   = "17073da9-c158-4119-bbc7-8748ddf64353";
        $headers = [
            'Authorization' => "Bearer {$token}",
            'Accept'        => 'application/json',
        ];
        return new Client([
            'base_uri' => 'https://api.upyun.com/',
            'headers'  => $headers,
        ]);
    }

    protected function createDns($domain, $value)
    {
        $subDomain = Str::substr($domain, 0, Str::length($domain) - Str::length(config('app.cname_host')) - 1);
        $data      = [
            'domain'      => config('app.cname_host'),
            'sub_domain'  => $subDomain,
            'record_type' => 'CNAME',
            'record_line' => '默认',
            'value'       => $value,
        ];

        $response = $this->getDnsClient()->post('/Record.Create', [
            'form_params' => $this->prepareDnsData($data),
        ]);

        $result = json_decode((string) $response->getBody(), true);

        $code = Arr::get($result, 'status.code');

        if ($code != 1 && $code != 104) {
            throw new CdnException(Arr::get($result, 'status.message'));
        }
    }

    protected function deleteDns($domain)
    {
        $id = $this->getDnsId($domain);
        if ($id) {
            $data = [
                'domain'    => config('app.cname_host'),
                'record_id' => $id,
            ];

            $this->getDnsClient()->post('/Record.Remove', [
                'form_params' => $this->prepareDnsData($data),
            ]);
        }
    }

    protected function getDnsId($domain)
    {
        $subDomain = Str::substr($domain, 0, Str::length($domain) - Str::length(config('app.cname_host')) - 1);

        $data = [
            'domain'      => config('app.cname_host'),
            'sub_domain'  => $subDomain,
            'record_type' => 'CNAME',
            'record_line' => '默认',
        ];

        $response = $this->getDnsClient()->post('/Record.List', [
            'form_params' => $this->prepareDnsData($data),
        ]);

        $result = json_decode((string) $response->getBody(), true);

        $records = Arr::get($result, 'records', []);
        if (count($records) > 0) {
            return $records[0]['id'];
        }
    }

    protected function prepareDnsData($data)
    {
        return array_merge(
            [
                'login_token'    => "298764,e222aa8128940540e7d25aaa5179d194",
                'format'         => 'json',
                'lang'           => 'cn',
                'error_on_empty' => 'no',
            ],
            $data,
        );
    }

    protected function getDnsClient()
    {
        return new Client([
            'base_uri' => 'https://dnsapi.cn',
        ]);
    }
}
