<?php

namespace app\lib\plan;

use app\lib\Date;

class EnterprisePlan extends BasePlan
{
    public $name  = 'enterprise';
    public $title = '企业版';

    public $features = [
        '不限空间成员',
        //'文档重用',
        '单点登录',
        '文档模板',
        '域名绑定',
        '10G媒体空间',
        '单文件100M上传',
        '快速发布',
        '专属客服',
    ];

    public $size = 10;

    public function action($name)
    {
        if ($name != $this->name) {
            if ($this->space->isExpired()) {
                return 'buy';
            }
            return 'downgrade';
        }

        return 'buy';
    }

    public function canDowngrade()
    {
        return $this->name != 'enterprise' || $this->space->expire_time->lt(Date::now()->addDays(7));
    }

    public function actions($button = false)
    {
        if ($button) {
            if ($this->canDowngrade()) {
                $team = "<a href='/-/org/billing/plan/team' class='btn btn-outline-secondary btn-sm'>降级到团队版</a>";
            } else {
                $team = "<a data-bs-toggle='tooltip' title='到期 7 天内才可以降级' class='btn btn-outline-secondary btn-sm'>降级到团队版</a>";
            }

            return [
                'team'       => $team,
                'enterprise' => "<a href='/-/org/billing/plan/enterprise' class='btn btn-primary btn-sm'>续费企业版</a>",
            ];
        }
        return [
            'team'       => '降级到团队版',
            'enterprise' => '续费企业版',
        ];
    }

}
