<?php

namespace app\lib\plan;

class TrialPlan extends EnterprisePlan
{
    public $name  = 'trial';
    public $title = '试用版';

    public function action($name)
    {
        return 'buy';
    }

    public function actions($button = false)
    {
        if ($button) {
            return [
                'team'       => "<a href='/-/org/billing/plan/team' class='btn btn-outline-primary btn-sm'>购买团队版</a>",
                'enterprise' => "<a href='/-/org/billing/plan/enterprise' class='btn btn-primary btn-sm'>购买企业版</a>",
            ];
        }
        return [
            'team'       => '购买团队版',
            'enterprise' => '购买企业版',
        ];
    }
}
