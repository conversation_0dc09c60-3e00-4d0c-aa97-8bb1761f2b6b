<?php

namespace app\lib\plan;

class TeamPlan extends BasePlan
{
    public $name  = 'team';
    public $title = '团队版';

    public $features = [
        '10个空间成员',
        '文档模板',
        '域名绑定',
        '2G媒体空间',
        '单文件20M上传',
    ];

    public $members = 10;

    public $size = 2;

    public function action($name)
    {
        if ($name != $this->name) {
            if ($this->space->isExpired()) {
                return 'buy';
            }
            return 'upgrade';
        }

        return 'buy';
    }

    public function actions($button = false)
    {
        if ($button) {
            return [
                'team'       => "<a href='/-/org/billing/plan/team' class='btn btn-primary btn-sm'>续费团队版</a>",
                'enterprise' => "<a href='/-/org/billing/plan/enterprise' class='btn btn-outline-primary btn-sm'>升级到企业版</a>",
            ];
        }
        return [
            'team'       => '续费团队版',
            'enterprise' => '升级到企业版',
        ];
    }
}
