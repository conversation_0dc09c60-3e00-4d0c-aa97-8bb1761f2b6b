<?php

namespace app\lib\plan;

use app\model\Space;
use BadMethodCallException;
use think\helper\Str;

abstract class BasePlan
{
    public $name;
    public $title;

    public $features;

    public $members = null;

    public $size = null;

    public function __construct(protected Space $space)
    {
    }

    public function action($name)
    {
        return null;
    }

    public function actions($button = false)
    {
        return null;
    }

    public function __toString(): string
    {
        return $this->name;
    }

    public function __call(string $name, array $arguments)
    {
        $method = "get" . Str::studly($name);
        if (!method_exists($this, $method)) {
            throw new BadMethodCallException('Call to undefined method ' . get_class($this) . '::' . $method . '()');
        }
        return $this->{$method}(...$arguments);
    }
}
