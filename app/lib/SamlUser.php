<?php

namespace app\lib;

use app\policy\NonePolicy;
use think\Model;
use think\model\concern\Virtual;
use yunwuxin\auth\interfaces\Authorizable;
use yunwuxin\auth\interfaces\PolicyResolver;
use yunwuxin\auth\traits\AuthorizableUser;

/**
 * @property integer $team
 */
class SamlUser extends Model implements PolicyResolver, Authorizable
{
    use Virtual, AuthorizableUser;

    public function resolvePolicy($class)
    {
        $policy = '\\app\\policy\\saml\\' . class_basename($class) . 'Policy';
        if (!class_exists($policy)) {
            return NonePolicy::class;
        }
        return $policy;
    }
}
