<?php

namespace app\lib\goods;

use app\lib\Date;
use app\lib\Goods;
use app\model\Space;

class Upgrade extends Goods
{
    public function __construct(public Space $space, public $name)
    {

    }

    public function getSubject()
    {
        return "升级到企业版";
    }

    public function getAmount()
    {
        $days  = $this->space->expire_time->diffInDays(Date::now());
        $price = floor($days / 365 * (Plan::PRICES[$this->name] - Plan::PRICES[$this->space->plan]));

        return $price * 100;
    }

    public function getReturnUrl(): ?string
    {
        return (string) url('/-/org/billing')->domain($this->space->domain);
    }

    public function getNotifyUrl(): ?string
    {
        return (string) url("/api/notify/upgrade/{$this->name}")->domain($this->space->domain);
    }
}
