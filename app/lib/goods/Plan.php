<?php

namespace app\lib\goods;

use app\lib\Goods;
use app\model\Space;

class Plan extends Goods
{
    const TEAM       = 'team';
    const ENTERPRISE = 'enterprise';

    const PRICES = [
        self::ENTERPRISE => 1888,
        self::TEAM       => 288,
    ];

    public function __construct(public Space $space, public $name, public $years)
    {

    }

    public function getSubject()
    {
        $title = [
            self::ENTERPRISE => '企业版',
            self::TEAM       => '团队版',
        ];
        return "{$title[$this->name]}{$this->years}年";
    }

    public function getAmount()
    {
        return (self::PRICES[$this->name] + $this->space->size * Size::PRICE + $this->space->tokens * Tokens::PRICE) * $this->years * 100;
    }

    public function getReturnUrl(): ?string
    {
        return (string) url('/-/org/billing')->domain($this->space->domain);
    }

    public function getNotifyUrl(): ?string
    {
        return (string) url("/api/notify/plan/{$this->name}", ['year' => $this->years])->domain($this->space->domain);
    }
}
