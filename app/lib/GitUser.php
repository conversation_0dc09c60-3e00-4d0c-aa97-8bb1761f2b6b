<?php

namespace app\lib;

use app\model\AccessToken;
use app\model\Book;
use app\model\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class GitUser
{
    /** @var AccessToken|null */
    protected $token;

    /** @var User|null */
    protected $user;

    public function getBasicToken()
    {
        $token = JWT::encode([
            'user'  => $this->user?->id,
            'token' => $this->token?->id,
            'iat'   => Date::now()->getTimestamp(),
            'exp'   => Date::now()->add(30, 'minutes')->getTimestamp(),
        ], config('app.token'), 'HS256');

        return base64_encode("jwt_token:{$token}");
    }

    public function getUser()
    {
        return $this->user;
    }

    public function canPull(Book $book)
    {
        if ($this->user) {
            return $this->user->can('read', $book);
        } elseif ($this->token) {
            return $this->token->isAvailableFor($book) &&
                (
                    $this->token->hasScope('read_repository') ||
                    $this->token->hasScope('write_repository')
                );
        }
        return false;
    }

    public function canPush(Book $book)
    {
        if ($this->user) {
            return $this->user->can('write', $book);
        } elseif ($this->token) {
            return $this->token->isAvailableFor($book) && $this->token->hasScope('write_repository');
        }
        return false;
    }

    public static function createByUser(User $user)
    {
        $gitUser       = new self();
        $gitUser->user = $user;
        return $gitUser;
    }

    public static function createByJWT($jwt)
    {
        $data = JWT::decode($jwt, new Key(config('app.token'), 'HS256'));

        $user  = isset($data->user) ? User::find($data->user) : null;
        $token = isset($data->token) ? AccessToken::find($data->token) : null;

        $gitUser = new self();

        $gitUser->user  = $user;
        $gitUser->token = $token;

        return $gitUser;
    }

    public static function createByToken(AccessToken $token)
    {
        $gitUser = new self();

        $gitUser->token = $token;

        return $gitUser;
    }
}
