<?php

namespace app\lib;

abstract class Goods
{
    use SerializesModel;

    abstract public function getSubject();

    abstract public function getAmount();

    public function getReturnUrl(): ?string
    {
        return null;
    }

    abstract public function getNotifyUrl(): ?string;

    public function getOrderNo()
    {
        return uniqid();
    }

    public function purchase()
    {
        $params = [
            'order_no'   => $this->getOrderNo(),
            'subject'    => $this->getSubject(),
            'amount'     => $this->getAmount(),
            'return_url' => $this->getReturnUrl(),
            'notify_url' => $this->getNotifyUrl(),
        ];

        /** @var Cloud $cloud */
        $cloud = app(Cloud::class);

        return $cloud->createCharge($params);
    }
}
