<?php

namespace app\lib;

use app\model\Setting;
use Exception;
use think\api\Client;
use think\helper\Arr;
use Throwable;

class Moderation
{
    public function text($text)
    {
        if (!$this->isAvailable()) {
            return null;
        }

        $checkText = function ($text, $tries = 0) use (&$checkText) {
            try {
                $tries++;

                $result = $this->getClient()
                    ->greenTextAdvance()
                    ->withService('llm_response_moderation')
                    ->withContent($text)
                    ->request();

                $code = Arr::get($result, 'code');
                if ($code !== 0) {
                    throw new Exception(Arr::get($result, 'message'));
                }

                $results = Arr::get($result, 'data.Result', []);

                $keywords = implode(',', array_map(function ($item) {
                    return $item['RiskWords'] ?? '';
                }, array_filter($results, function ($item) {
                    return $item['Label'] != 'nonLabel';
                })));

                if (!empty($keywords)) {
                    return $keywords;
                }
                return null;
            } catch (Throwable $e) {
                if ($tries <= 3) {
                    return $checkText($text, $tries);
                }
                return $e->getMessage();
            }
        };

        //去除emoji
        $regex = '/[\x{1F600}-\x{1F64F}\x{1F300}-\x{1F5FF}\x{1F680}-\x{1F6FF}\x{1F700}-\x{1F77F}\x{1F780}-\x{1F7FF}\x{1F800}-\x{1F8FF}\x{1F900}-\x{1F9FF}\x{2600}-\x{26FF}\x{2700}-\x{27BF}]/u';
        $text  = preg_replace($regex, '', $text);

        //去除class等代码标识，防止被阿里云防火墙屏蔽
        $text = str_ireplace(['class', 'function', 'env:PATH', '{', '}', 'eval', '.'], "", $text);

        $chunks = mb_str_split($text, 5000);

        foreach ($chunks as $chunk) {
            $chunk = trim($chunk);
            if (!empty($chunk)) {
                $reason = $checkText($chunk);
                if (!empty($reason)) {
                    return $reason;
                }
            }
        }

        return null;
    }

    public function isAvailable()
    {
        return Setting::read('feature.moderation') && Setting::read('feature.moderation.token');
    }

    protected function getClient()
    {
        return new Client(Setting::read('feature.moderation.token'));
    }
}
