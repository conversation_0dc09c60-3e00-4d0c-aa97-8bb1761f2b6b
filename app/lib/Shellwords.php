<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib;

class Shellwords
{
    static function split($line)
    {
        $line    .= ' ';
        $pattern = '/\G\s*(?>([^\s\\\'\"]+)|\'([^\']*)\'|"((?:[^\"\\\\]|\\.)*)"|(\\.?)|(\S))(\s|\z)?/m';
        preg_match_all($pattern, $line, $matches, PREG_SET_ORDER);
        $words = [];
        $field = '';
        foreach ($matches as $set) {
            # Index #0 is the full match.
            array_shift($set);
            @list($word, $sq, $dq, $esc, $garbage, $sep) = $set;
            if ($garbage) {
                throw new \UnexpectedValueException("Unmatched double quote: '$line'");
            }
            $field .= ($dq ?: $sq ?: $word);
            if (strlen($sep) > 0) {
                $words[] = $field;
                $field   = '';
            }
        }
        return $words;
    }

    static function join($pieces)
    {
        return join(' ', array_map("escapeshellarg", $pieces));
    }
}
