<?php

namespace app\lib;

use app\model\AccessToken;
use Exception;
use yunwuxin\auth\credentials\PasswordCredential;
use yunwuxin\auth\provider\Model;

class GitUserProvider extends Model
{

    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof PasswordCredential) {
            $username = $credentials->getUsername();
            $password = $credentials->getPassword();

            try {
                switch ($username) {
                    case 'jwt_token':
                        return GitUser::createByJWT($password);
                    case 'access_token':
                        $accessToken = AccessToken::getByToken($password);
                        if ($accessToken) {
                            return GitUser::createByToken($accessToken);
                        }
                        break;
                    default:
                        $user = parent::retrieveByCredentials($credentials);
                        if ($user) {
                            return GitUser::createByUser($user);
                        }
                }
            } catch (Exception) {

            }
        }
    }
}
