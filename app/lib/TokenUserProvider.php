<?php

namespace app\lib;

use app\model\AccessToken;
use app\model\Space;
use app\model\User;
use app\Request;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\helper\Str;
use yunwuxin\auth\credentials\RequestCredentials;
use yunwuxin\auth\interfaces\Provider;

class TokenUserProvider implements Provider
{

    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof RequestCredentials) {
            $request = $credentials->getRequest();

            $token = $this->getAccessTokenFromRequest($request);

            if (!empty($token)) {
                try {
                    //先尝试JWT
                    $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));

                    $user  = User::find($decoded['user']);
                    $space = Space::find($decoded['space']);

                    $request->setSpace($space);

                    return $user;
                } catch (Exception) {
                    /** @var AccessToken $accessToken */
                    $accessToken = AccessToken::getByToken($token, Space::class, scope: 'api');
                    if ($accessToken && $accessToken->isAvailableFor($request->getSpace())) {
                        return TokenUser::createByToken($accessToken);
                    }
                }
            }
        }
    }

    protected function getAccessTokenFromRequest(Request $request)
    {
        $token = $request->param('access-token');
        if (empty($token)) {
            $header = $request->header('Authorization');
            if (!empty($header)) {
                if (Str::startsWith($header, 'Token ')) {
                    $token = Str::substr($header, 6);
                }
                if (Str::startsWith($header, 'Bearer ')) {
                    $token = Str::substr($header, 7);
                }
            }
        }

        return $token;
    }
}
