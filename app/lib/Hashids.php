<?php

namespace app\lib;

use think\exception\HttpException;
use Throwable;

class Hashids
{
    protected static function hashids($salt = null)
    {
        return new \Hashids\Hashids($salt ?: env('HASHIDS_SALT', ''), 10, 'abcdefghijklmnopqrstuvwxyz123456789');
    }

    public static function encode($id, $salt = null)
    {
        return self::hashids($salt)->encode($id);
    }

    public static function decode($hash, $force = true, $salt = null)
    {
        try {
            [$id] = self::hashids($salt)->decode($hash);
        } catch (Throwable) {
            $id = null;
        }

        if (empty($id) && $force) {
            throw new HttpException(404);
        }

        return $id;
    }

}
