<?php

namespace app\lib;

use app\model\AccessToken;
use app\model\Space;
use app\policy\NonePolicy;
use think\model\concern\Virtual;
use yunwuxin\auth\interfaces\Authorizable;
use yunwuxin\auth\interfaces\PolicyResolver;
use yunwuxin\auth\traits\AuthorizableUser;

class TokenUser implements PolicyResolver, Authorizable
{
    use Virtual, AuthorizableUser;

    /** @var AccessToken */
    protected $token;

    public static function createByToken(AccessToken $token)
    {
        $user = new self();

        $user->token = $token;

        return $user;
    }

    public function getToken()
    {
        return $this->token;
    }

    /**
     * @return \app\model\Space|null
     */
    public function getSpace()
    {
        if ($this->token->accessible instanceof Space) {
            return $this->token->accessible;
        }
        return null;
    }

    public function resolvePolicy($class)
    {
        $policy = '\\app\\policy\\token\\' . class_basename($class) . 'Policy';
        if (!class_exists($policy)) {
            return NonePolicy::class;
        }
        return $policy;
    }
}
