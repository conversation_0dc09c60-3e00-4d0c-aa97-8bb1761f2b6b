<?php

namespace app\lib\lfs;

use app\model\Book;
use app\model\LfsObject;
use RuntimeException;
use think\console\Output;
use think\facade\Filesystem;
use think\File;
use think\helper\Str;

class LfsTransfer
{
    const MaxPacketLength = 65516;

    public function __construct(protected Book $book, protected $operation, protected $input, protected $output)
    {
    }

    public function start()
    {
        $this->writeMessage('version=1');

        while (true) {
            $packet = $this->readPacket();
            if (!$packet) {
                return;
            }

            switch (true) {
                case Str::startsWith($packet->head, 'version'):
                    if ($packet->head != 'version 1') {
                        throw new RuntimeException('Unable to negotiate version with local side (missing version 1)');
                    }

                    $this->writeMessage('status 200');
                    break;
                case $packet->head == 'batch':
                    $lines = iterator_to_array($packet->readData());

                    switch ($this->operation) {
                        case 'download':
                            $lines = array_map(function ($line) {
                                [$oid, $size] = explode(' ', $line);
                                return "{$oid} {$size} download";
                            }, $lines);
                            break;
                        case 'upload':
                            $lines = array_map(function ($line) {
                                [$oid, $size] = explode(' ', $line);
                                $object = $this->book->lfsObjects()
                                    ->where('oid', $oid)->where('size', $size)
                                    ->find();

                                if ($object) {
                                    return "{$oid} {$size} noop";
                                }

                                return "{$oid} {$size} upload";
                            }, $lines);
                            break;
                    }

                    $this->writeStatusWithLines(
                        200,
                        $packet->arguments,
                        $lines
                    );
                    break;
                case Str::startsWith($packet->head, 'get-object'):
                    [, $oid] = explode(' ', $packet->head);
                    $object = LfsObject::where('oid', $oid)->find();

                    if ($object) {
                        $file = Filesystem::disk('lfs')->path($object->filename);
                        $this->writeStatusWithData(
                            200,
                            $packet->arguments,
                            fopen($file, 'r')
                        );
                    } else {
                        $this->writeStatusWithLines(404);
                    }
                    break;
                case Str::startsWith($packet->head, 'put-object'):
                    [, $oid] = explode(' ', $packet->head);

                    $tempFile = tempnam(runtime_path('temp'), 'LFS');
                    try {
                        $resource = fopen($tempFile, 'r+');

                        foreach ($packet->readData(false) as $d) {
                            fwrite($resource, $d);
                        }

                        fclose($resource);

                        if ($oid == hash_file('sha256', $tempFile)) {
                            $object = LfsObject::findOrCreate([
                                'oid'  => $oid,
                                'size' => $packet->arguments['size'],
                            ]);

                            $file = new File($tempFile);
                            Filesystem::disk('lfs')->putFileAs($object->filename, $file, '');
                            $object->books()->attach($this->book);

                            $this->writeStatusWithLines(200);
                        } else {
                            $this->writeStatusWithLines(400);
                        }
                    } finally {
                        unlink($tempFile);
                    }
                    break;
                case $packet->head == 'quit':
                    $this->writeStatusWithLines(200);
                    break 2;
                case Str::startsWith($packet->head, 'verify-object'):
                    $this->writeStatusWithLines(200);
                    break;
                default:
                    $this->writeMessage('status 400');
                    break;
            }
        }
    }

    protected function readPacket()
    {
        $pktLenHex = fread($this->input, 4);
        $pktLen    = hexdec($pktLenHex);
        if ($pktLen == 0 || $pktLen == 1) {
            return null;
        }
        if ($pktLen < 4) {
            throw new RuntimeException('Invalid packet length.');
        }
        $head = fread($this->input, $pktLen - 4);

        return new Packet(trim($head, "\n"), $this->input);
    }

    protected function writeStatusWithLines($status, $args = [], $lines = null)
    {
        $this->writePacketText("status {$status}");
        $this->writeArguments($args);

        if ($lines) {
            $this->writeDelim();
            foreach ($lines as $line) {
                $this->writePacketText($line);
            }
        }
        $this->writeFlush();
    }

    protected function writeStatusWithData($status, $args, $data)
    {
        $this->writePacketText("status {$status}");

        $this->writeArguments($args);

        $this->writeDelim();

        $length = self::MaxPacketLength - 4;

        while (!feof($data)) {
            $this->writePacket(fread($data, $length));
        }
        fclose($data);

        $this->writeFlush();
    }

    protected function writeArguments($args = [])
    {
        foreach ($args as $key => $value) {
            if ($value === true) {
                $this->writePacketText($key);
            } else {
                $this->writePacketText("{$key}={$value}");
            }
        }
    }

    protected function writeMessage($command)
    {
        $this->writePacketText($command);
        $this->writeFlush();
    }

    protected function writePacket($data)
    {
        $this->write(sprintf('%04x', strlen($data) + 4) . $data);
    }

    protected function writeDelim()
    {
        $this->write(sprintf('%04x', 1));
    }

    protected function writeFlush()
    {
        $this->write(sprintf('%04x', 0));
    }

    protected function writePacketText($data)
    {
        $this->writePacket($data . "\n");
    }

    protected function write($str)
    {
        $this->output->write($str, false, Output::OUTPUT_RAW);
    }

}
