<?php

namespace app\lib\lfs;

use RuntimeException;

class Packet
{
    public $arguments = [];

    public function __construct(public $head, protected $input)
    {
        $this->init();
    }

    public function readData($trim = true)
    {
        while (true) {
            $pktLenHex = fread($this->input, 4);

            $pktLen = hexdec($pktLenHex);

            if ($pktLen == 0) {
                return;
            }

            if ($pktLen < 4) {
                throw new RuntimeException('Invalid packet length.');
            }
            $data = $this->read($pktLen - 4);

            if ($trim) {
                $data = rtrim($data, "\n");
            }
            yield $data;
        }
    }

    protected function read($length)
    {
        $data = '';
        do {
            $data .= fread($this->input, $length - strlen($data));
        } while (strlen($data) < $length);
        return $data;
    }

    protected function init()
    {
        while (true) {
            $pktLenHex = fread($this->input, 4);
            $pktLen    = hexdec($pktLenHex);

            // 0: flush packet, 1: delim packet
            if ($pktLen == 0 || $pktLen == 1) {
                break;
            }

            if ($pktLen < 4) {
                throw new RuntimeException('Invalid packet length.');
            }

            $data = trim(fread($this->input, $pktLen - 4));

            if (strpos($data, '=') !== false) {
                [$key, $value] = explode('=', $data);
            } else {
                $key   = $data;
                $value = true;
            }

            $this->arguments[$key] = $value;
        }
    }
}
