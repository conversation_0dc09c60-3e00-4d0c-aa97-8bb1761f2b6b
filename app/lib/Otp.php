<?php

namespace app\lib;

use think\Session;

class Otp
{
    public function __construct(protected Session $session)
    {

    }

    public function verify(string $id, $code, $prefix = '')
    {
        $key     = $this->key($id, $prefix);
        $payload = $this->session->get($key);

        if ($payload && $code == $payload['code'] && time() - 60 * 10 < $payload['time']) {
            $this->session->delete($key);
            return true;
        }
        return false;
    }

    public function create(string $id, $prefix = '')
    {
        $code = rand(100000, 999999);

        $this->session->set($this->key($id, $prefix), [
            'code' => $code,
            'time' => time(),
        ]);

        return $code;
    }

    protected function key(string $id, $prefix = '')
    {
        return md5(self::class . '.' . $prefix . $id);
    }
}
