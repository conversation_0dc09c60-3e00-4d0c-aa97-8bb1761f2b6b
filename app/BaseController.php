<?php

declare(strict_types = 1);

namespace app;

use app\model\User;
use Closure;
use think\App;
use think\db\Query;
use think\Model;
use think\Validate;
use yunwuxin\Auth;
use yunwuxin\auth\exception\AuthorizationException;
use yunwuxin\facade\Gate;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \app\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /** @var User|\app\lib\TokenUser|null */
    protected $user;

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app, Auth $auth)
    {
        $this->app     = $app;
        $this->request = $this->app->request;
        $this->user    = $auth->user();
    }

    protected function middleware($middleware, ...$params)
    {
        $options = [];

        $this->middleware[] = [
            'middleware' => [$middleware, $params],
            'options'    => &$options,
        ];

        return new class($options) {
            protected $options;

            public function __construct(array &$options)
            {
                $this->options = &$options;
            }

            public function only($methods)
            {
                $this->options['only'] = is_array($methods) ? $methods : func_get_args();
                return $this;
            }

            public function except($methods)
            {
                $this->options['except'] = is_array($methods) ? $methods : func_get_args();

                return $this;
            }
        };
    }

    protected function authorized($ability, ...$args)
    {
        $result = Gate::raw($ability, ...$args);

        if ($result !== true) {
            throw new AuthorizationException();
        }
    }

    // 初始化
    protected function initialize()
    {
    }

    /**
     * 验证数据
     * @access protected
     * @param string|array $validate 验证器名或者验证规则数组
     * @param array $message 提示信息
     * @return array
     */
    protected function validate($validate, array $message = [])
    {
        $v = new Validate();
        $v->rule(array_filter($validate));
        $v->message($message);
        $v->batch(true);

        $params = $this->request->all();

        $v->failException(true)->check($params);

        $data = [];

        foreach ($validate as $key => $rule) {
            if (strpos($key, '|')) {
                // 字段|描述 用于指定属性名称
                [$key] = explode('|', $key);
            }
            if (array_key_exists($key, $params)) {
                $data[$key] = $params[$key];
            }
        }

        return $data;
    }

    /**
     * @param Query|Model|\think\model\Relation $query
     * @param $fields
     * @return void
     */
    protected function filterFields($query, $fields)
    {
        foreach ($fields as $key => $value) {
            if (is_string($key)) {
                if ($this->request->has($key)) {
                    if ($value === true) {
                        $query->where($key, $this->request->param($key));
                    } elseif (is_callable($value)) {
                        call_user_func($value, $query, $this->request->param($key));
                    }
                }
            } elseif ($this->request->has($value)) {
                $condition = $this->request->param($value);
                if (is_array($condition)) {
                    $query->whereIn($value, $condition);
                } else {
                    $query->whereLike($value, "%{$this->request->param($value)}%");
                }
            }
        }
    }

    /**
     * @param Query|Model|\think\model\Relation $query
     * @param $field
     * @param $q
     * @return void
     */
    protected function searchField($query, $field, $q = 'q', Closure $search = null)
    {
        if ($this->request->has($q)) {
            $qs      = $this->request->param($q);
            $default = function ($query, $field, $qs) {
                $query->whereLike($field, "%{$qs}%");
            };
            if ($search) {
                $search($query, $field, $qs, $default);
            } else {
                $default($query, $field, $qs);
            }
        }
    }
}
