<?php

namespace app\command\git;

use app\model\PublicKey;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;

class AuthorizedKeysCheck extends Command
{
    const SSH_OPTIONS = 'no-port-forwarding,no-X11-forwarding,no-agent-forwarding,no-pty';

    protected function configure()
    {
        // 指令配置
        $this
            ->setName('git:authorized-keys-check')
            ->addArgument('user', Argument::REQUIRED, 'user')
            ->addArgument('key', Argument::REQUIRED, 'key');
    }

    protected function execute(Input $input, Output $output)
    {
        $user = $input->getArgument('user');
        $key  = $input->getArgument('key');

        if ($user == 'git') {
            $fingerprint = PublicKey::insecureKeyFingerprint($key);

            $publicKey = PublicKey::where('fingerprint', $fingerprint)->findOrFail();

            $output->write($this->keyLine($publicKey));
        }
    }

    protected function keyLine(PublicKey $publicKey)
    {
        $command = sprintf('/sbin/think.sh git:shell %s', $publicKey->id);

        return sprintf('command="%s",%s %s', $command, self::SSH_OPTIONS, $publicKey->key);
    }
}
