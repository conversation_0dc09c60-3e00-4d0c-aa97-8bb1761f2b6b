<?php

namespace app\command\git;

use app\lib\Hashids;
use app\model\Book;
use app\model\LfsObject;
use think\console\Command;
use think\console\Output;
use think\File;
use think\Filesystem;
use think\helper\Str;

class Lfs extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('git:lfs');
    }

    public function handle(Filesystem $filesystem)
    {
        while ($f = fgets(STDIN)) {
            $request = json_decode($f);

            try {
                $disk = $filesystem->disk('lfs');

                switch ($request->event) {
                    case 'init':
                        if ($request->operation == 'upload') {
                            if (Str::contains($request->remote, '/')) {
                                [$id, $rid] = explode('/', $request->remote);
                            } else {
                                $id = $request->remote;
                            }
                            $book = Book::findOrFail(Hashids::decode($id));

                            if (!empty($rid)) {
                                $original = Book::findOrFail(Hashids::decode($rid));

                                $original->lfsObjects()->chunk(20, function ($objects) use ($book) {
                                    /** @var LfsObject $object */
                                    foreach ($objects as $object) {
                                        $object->books()->attach($book);
                                    }
                                });
                            }
                        }
                        $this->output->writeln('{}');
                        break;
                    case 'download':
                        $object   = LfsObject::where('oid', $request->oid)->findOrFail();
                        $file     = $disk->path($object->filename);
                        $tempFile = tempnam(runtime_path('temp'), 'LFS');
                        copy($file, $tempFile);

                        $this->response([
                            'event' => 'complete',
                            'oid'   => $request->oid,
                            'path'  => $tempFile,
                        ]);
                        break;
                    case 'upload':
                        if (empty($book)) {
                            throw new \Exception('Book not found');
                        }
                        $object = LfsObject::findOrCreate([
                            'oid'  => $request->oid,
                            'size' => $request->size,
                        ]);

                        $file = new File($request->path);
                        $disk->putFileAs($object->filename, $file, '');

                        $object->books()->attach($book);

                        $this->response([
                            'event' => 'complete',
                            'oid'   => $request->oid,
                        ]);

                        break;
                    case 'terminate':
                        break 2;
                }
            } catch (\Exception $e) {
                $data = [];
                if ($request->oid) {
                    $data['event'] = 'complete';
                    $data['oid']   = $request->oid;
                }
                $this->response(array_merge($data, ['error' => ['code' => 0, 'message' => $e->getMessage()]]));
            }
        }
    }

    protected function response($data = [])
    {
        $this->output->writeln(json_encode(new \ArrayObject($data)), Output::OUTPUT_RAW);
    }
}
