<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\command\git;

use app\lib\GitUser;
use app\lib\Hashids;
use app\lib\lfs\LfsTransfer;
use app\lib\Shellwords;
use app\model\Book;
use app\model\PublicKey;
use app\model\Space;
use Exception;
use InvalidArgumentException;
use Symfony\Component\Process\Process;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\facade\Log;
use think\helper\Str;

class Shell extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('git:shell')
            ->addArgument('id', Argument::REQUIRED, 'id');
    }

    public function execute(Input $input, Output $output)
    {
        if (!isset($_SERVER['SSH_CONNECTION'])) {
            throw new Exception('Only ssh allowed');
        }

        if (!empty($_SERVER['SSH_ORIGINAL_COMMAND'])) {
            $words = Shellwords::split($_SERVER['SSH_ORIGINAL_COMMAND']);
            $verb  = $words[0];

            if (!Str::startsWith($verb, 'git-')) {
                throw new InvalidArgumentException('Unknown git command');
            }

            ['dirname' => $dirname, 'filename' => $filename] = pathinfo(ltrim($words[1], '/'));

            $space = Space::findOrFail(Hashids::decode($dirname));
            /** @var Book $book */
            $book = $space->books()->findOrFail(Hashids::decode($filename));

            $publicKey = PublicKey::where('space_id', $space->id)
                ->where('id', $input->getArgument('id'))
                ->findOrFail();

            $user = GitUser::createByUser($publicKey->user);

            try {
                switch ($verb) {
                    case 'git-lfs-transfer':
                        $operation = $words[2];
                        $transfer  = new LfsTransfer($book, $operation, STDIN, $output);
                        $transfer->start();
                        break;
                    case 'git-lfs-authenticate':
                        $operation = $words[2];

                        $output->write(json_encode([
                            'href'   => (string) url("/{$book->hash_id}.git/info/lfs")->domain($space->domain),
                            'header' => [
                                'Authorization' => 'Basic ' . $user->getBasicToken(),
                            ],
                        ]));
                        break;
                    default:
                        $payload = [
                            'book' => $book->id,
                            'user' => $user->getUser()?->id,
                        ];

                        $env = [
                            'HOME'              => $_SERVER['HOME'],
                            'PATH'              => $_SERVER['PATH'],
                            'GL_PROTOCOL'       => 'ssh',
                            'GIT_HOOKS_PAYLOAD' => base64_encode(json_encode($payload)),
                        ];

                        $process = new Process([$verb, $book->repo_path], $book->repo_path, $env, STDIN);

                        $process->setTimeout(0);
                        $process->setIdleTimeout(0);
                        $process->disableOutput();
                        $process->run(function ($type, $out) {
                            $this->output->write($out, false, Output::OUTPUT_RAW);
                        });
                }
            } catch (\Throwable $e) {
                Log::error($e->getMessage());
                Log::error($e->getTraceAsString());
                throw $e;
            }
        } else {
            $output->write('Welcome to TopThink Wiki.');
        }
    }
}
