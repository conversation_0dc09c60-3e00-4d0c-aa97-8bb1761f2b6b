<?php

namespace app\command\git;

use app\event\GitPush;
use app\model\Book;
use app\model\User;
use think\console\Command;
use think\console\input\Argument;
use think\Event;

class Hooks extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('git:hooks')
            ->addArgument('hook', Argument::REQUIRED)
            ->addArgument('args', Argument::IS_ARRAY);
    }

    public function handle(Event $event)
    {
        $hook = $this->input->getArgument('hook');
        $args = $this->input->getArgument('args');
        try {
            if (!empty($_SERVER['GIT_HOOKS_PAYLOAD'])) {
                $payload = json_decode(base64_decode($_SERVER['GIT_HOOKS_PAYLOAD']), true);

                $book = Book::findOrFail($payload['book']);
                if ($payload['user']) {
                    //TODO 匿名用户
                    $user = User::findOrFail($payload['user']);
                    switch ($hook) {
                        case 'update':
                            [$ref, $old, $new] = $args;
                            $event->trigger(new GitPush($user, $book, $ref, $old, $new));
                            break;
                    }
                }
            }
        } catch (\Exception) {

        }
    }
}
