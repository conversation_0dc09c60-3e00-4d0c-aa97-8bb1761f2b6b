<?php

namespace app\command;

use app\lib\Cdn;
use app\model\Domain;
use think\console\Command;
use think\console\input\Option;

class UpdateCdnOptions extends Command
{
    protected function configure()
    {
        $this->setName('cdn:update')
            ->addOption('domain', 'd', Option::VALUE_OPTIONAL);
    }

    public function handle(Cdn $cdn)
    {
        $updateDomain = function (Domain $domain) use ($cdn) {
            if ($domain->space) {
                $bucketName = $cdn->getBucketName($domain);

                //更新配置
                $cdn->updateBucketMirror($bucketName);

                $this->output->writeln("{$domain->name} updated");
            } else {
                //空间已删除
                //TODO 删除域名并关闭CDN
                //这里的关联查询需要支持查询已删除的space并判断是否已软删除
                $this->output->writeln("{$domain->name} skipped");
            }
        };

        $name = $this->input->getOption('domain');

        if ($name) {
            $domain = Domain::where('name', $name)->findOrFail();
            $updateDomain($domain);
        } else {
            Domain::chunk(10, function ($domains) use ($updateDomain) {
                /** @var Domain $domain */
                foreach ($domains as $domain) {
                    $updateDomain($domain);
                }
            });
        }

    }
}
