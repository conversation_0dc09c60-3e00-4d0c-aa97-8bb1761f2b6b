<?php
declare (strict_types = 1);

namespace app;

use app\model\Setting;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Utils;
use League\Uri\Uri;
use think\ai\Client;
use think\App;
use think\facade\Config;
use think\Service;
use think\Validate;

/**
 * 应用服务类
 */
class AppService extends Service
{
    public function register()
    {
        // 服务注册
        $this->app->resolving(function ($instance, App $container) {
            if ($instance instanceof BaseController) {
                $container->invoke([$instance, 'initialize'], [], true);
            }
        });

        //AI Client
        $this->app->bind(Client::class, function (App $app, \think\Config $config) {
            $enable = $config->get('cloud.enable');

            if ($enable) {
                $token = $app->make(\TopThinkCloud\Client::class)->getClientToken();
            } else {
                $token = env('LICENSE', '');
            }

            $handler = new HandlerStack(Utils::chooseHandler());

            $client = new Client($token, $handler);
            $client->setEndpoint(env('AI_HOST', 'https://ai.topthink.com'));

            return $client;
        });
    }

    public function boot()
    {
        // 服务启动
        Validate::maker(function (Validate $validate) {
            $validate->extend('slug', function ($value) {
                return !!preg_match('/^[a-z][a-z0-9_-]{3,63}$/u', $value);
            }, ':attribute只能包含小写字母、数字，以及“-”和“_”符号,并且只能小写字母开头，长度为4-64个字符');
        });

        Config::hook(function ($name, $value) {
            switch ($name) {
                case 'app.url':
                    if (empty($value)) {
                        if ($this->app->exists(\think\Request::class)) {
                            return $this->app->make(\think\Request::class)->domain();
                        }
                    }
                    break;
                case 'app.host':
                    if (empty($value)) {
                        $url = config('app.url');
                        return Uri::createFromString($url)->getHost();
                    }
                    break;
                case 'app.token':
                    if (empty($value)) {
                        return env('LICENSE', 'TOKEN');
                    }
                    break;
                case 'cookie':
                    if (empty($value['domain'])) {
                        $value['domain'] = is_saas() ? ('.' . config('app.host')) : '';
                    }
                    break;
                case 'mail.transports.smtp':
                    return [
                        'host'       => Setting::read('mail.smtp_host'),
                        'port'       => Setting::read('mail.smtp_port', 465),
                        'encryption' => Setting::read('mail.smtp_encryption', 'ssl'),
                        'username'   => Setting::read('mail.smtp_username'),
                        'password'   => Setting::read('mail.smtp_password'),
                    ];
                case 'mail.from':
                    return [
                        'address' => Setting::read('mail.from_address'),
                        'name'    => Setting::read('mail.from_name', '知识管理'),
                    ];
            }
            return $value;
        });
    }
}
