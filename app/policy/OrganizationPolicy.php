<?php

namespace app\policy;

use app\model\Member;
use app\model\Organization;
use app\model\User;

class OrganizationPolicy extends BasePolicy
{

    public function before(?User $user, $ability)
    {
        //系统管理员拥有所有空间的权限
        if ($user && $user->hasPermission('admin')) {
            return true;
        }
    }

    public function owner(User $user, Organization $organization)
    {
        return $organization->hasMember($user, Member::OWNER);
    }

    public function admin(User $user, Organization $organization)
    {
        return $organization->hasMember($user, Member::MASTER);
    }
}
