<?php

namespace app\policy\saml;

use app\lib\SamlUser;
use app\model\Book;
use app\model\Team;
use think\db\Query;

class BookPolicy
{
    public function browse(SamlUser $user, Book $book)
    {
        if ($book->visibility_level >= Book::LEVEL_PUBLIC) {
            return true;
        }

        if ($book->visibility_level >= Book::LEVEL_PROTECTED) {
            return true;
        }

        return !!$book->members()->where(function (Query $query) use ($user) {
            $query->where('member_type', Team::class);
            $query->whereIn('member_id', $user->team);
        })->find();
    }
}
