<?php

namespace app\policy;

use app\exception\SpaceBlockedException;
use app\exception\SpaceExpiredException;
use app\model\Member;
use app\model\Space;
use app\model\User;

class SpacePolicy extends BasePolicy
{

    public function before(?User $user, $ability)
    {
        //系统管理员拥有所有空间的权限
        if ($user && $user->hasPermission('admin')) {
            return true;
        }
    }

    public function browse(?User $user, Space $space)
    {
        if ($space->isExpired()) {
            throw new SpaceExpiredException;
        }

        if ($space->isBlocked()) {
            throw new SpaceBlockedException;
        }
    }

    public function owner(User $user, Space $space)
    {
        if ($space->isOrg()) {
            return $space->owner->hasMember($user, Member::OWNER);
        }
        return false;
    }

    public function admin(User $user, Space $space, ?User $member = null)
    {
        if ($space->isOrg()) {
            $isMaster = $space->owner->hasMember($user, Member::MASTER);

            if (!empty($member)) {
                //检测是否对该成员有管理权限
                $isMemberMaster = $space->owner->hasMember($member, Member::MASTER);

                if ($isMemberMaster) {
                    return $space->owner->hasMember($user, Member::OWNER);
                }
            }

            return $isMaster;
        }
        return false;
    }

    public function create(User $user, Space $space)
    {
        if ($space->isOrg()) {
            return $space->owner->hasMember($user, Member::CREATOR);
        }
        return true;
    }

    public function read(User $user, Space $space)
    {
        if ($space->isOrg()) {
            return $space->owner->hasMember($user, Member::READER);
        } else {
            return $space->owner->id === $user->id;
        }
    }

    public function ai(User $user, Space $space)
    {
        if ($space->isOrg()) {
            return $space->owner->hasMember($user, Member::READER);
        }
        return false;
    }

}
