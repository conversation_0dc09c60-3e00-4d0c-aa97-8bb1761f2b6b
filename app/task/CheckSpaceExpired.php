<?php

namespace app\task;

use app\lib\Date;
use app\model\Space;
use TopThinkCloud\Notification\Notice;
use yunwuxin\cron\Task;

class CheckSpaceExpired extends Task
{
    protected function configure()
    {
        $this->onOneServer()->dailyAt('16:00');
    }

    protected function execute()
    {
        //查找3天后过期的空间
        $start = Date::now()->addDays(3)->startOfDay();
        $end   = $start->copy()->addDay();

        $spaces = Space::where('expire_time', 'between', [$start, $end])->select();

        foreach ($spaces as $space) {
            Notice::create("您的空间[{$space->title}]即将在{$space->expire_time->toDateString()}后到期，为了不影响功能使用，请及时续费。", sms: true)
                ->notify($space->owner->owners);
        }
    }
}
