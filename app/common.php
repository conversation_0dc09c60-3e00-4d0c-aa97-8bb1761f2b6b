<?php
// 应用公共文件

use app\lib\Date;
use app\model\Member;
use app\model\Organization;
use app\model\Setting;
use think\facade\Cache;
use TopThinkCloud\Client;
use TopThinkCloud\Exception\ValidationFailedException;

function array_flat_map(callable $fn, array $array): array
{
    return array_merge(...array_map($fn, $array));
}

function asset($path)
{
    static $manifest = null;
    if (!isset($manifest) || app()->isDebug()) {
        $manifestDirectory = root_path('asset/frontend/dist/asset');

        $manifestPath = $manifestDirectory . '/manifest.json';
        if (!is_file($manifestPath)) {
            throw new Exception('The manifest does not exist.');
        }

        $manifest = json_decode(file_get_contents($manifestPath), true);
    }

    if (!isset($manifest[$path])) {
        throw new Exception("Unable to locate file: {$path}.");
    }

    return $manifest[$path];
}

/**
 * 是否为云服务
 * @return mixed
 */
function is_saas()
{
    return env('CLOUD_ENABLE', false);
}

/**
 * @return \app\model\Space
 */
function get_default_space()
{
    //查找第一个组织
    $org = Organization::order('id asc')->find();
    if (empty($org)) {
        //单组织模式,创建默认组织
        $org = Organization::create([
            'name' => '知识管理',
        ]);
        $org->addMember(1, Member::OWNER);
    }
    return $org->space;
}

function get_license_id()
{
    return Cache::remember(env('LICENSE', 'license'), function (Client $client) {
        try {
            $info = $client->license()->verify('wiki', env('LICENSE'));
            return $info['id'];
        } catch (Exception $e) {
            if ($e instanceof ValidationFailedException) {
                abort(403, '应用尚未授权');
            }
            throw $e;
        }
    });
}

function setting($name, $default = null)
{
    return Setting::read($name, $default);
}

function parse_navs($str)
{
    $navs = [];
    foreach (explode("\n", trim($str)) as $line) {
        [$title, $url, $target] = array_pad(explode('|', trim($line), 3), 3, null);
        $navs[] = [
            'title'  => $title,
            'url'    => $url,
            'target' => $target,
        ];
    }

    return $navs;
}

function classnames()
{
    $args = func_get_args();

    $data = array_reduce($args, function ($carry, $arg) {
        if (is_array($arg)) {
            return array_merge($carry, $arg);
        }

        $carry[] = $arg;
        return $carry;
    }, []);

    $classes = array_map(function ($key, $value) {
        $condition = $value;
        $return    = $key;

        if (is_int($key)) {
            $condition = null;
            $return    = $value;
        }

        $isArray          = is_array($return);
        $isObject         = is_object($return);
        $isStringableType = !$isArray && !$isObject;

        $isStringableObject = $isObject && method_exists($return, '__toString');

        if (!$isStringableType && !$isStringableObject) {
            return null;
        }

        if ($condition === null) {
            return $return;
        }

        return $condition ? $return : null;
    }, array_keys($data), array_values($data));

    $classes = array_filter($classes);

    return implode(' ', $classes);
}

function format_bytes($size, $format = 'GiB/00')
{
    if ($size === null) {
        return '--';
    }
    return \ByteUnits\Binary::bytes($size)->format($format);
}

function format_tokens($tokens)
{
    return number_format($tokens / 1000, 2) . 'K';
}

function is_valid_domain($domain)
{
    return (boolean) preg_match('/^(?=^.{3,255}$)[a-z0-9][-a-z0-9]{0,62}(\.[a-z0-9][-a-z0-9]{0,62})+$/i', $domain);
}

function main_url($url)
{
    return (string) url($url)->domain(config('app.url'));
}

function space_url(\app\model\Space $space, $url, $parked = false)
{
    if ($parked) {
        if ($space->parked_domain) {
            return (string) url($url)->domain($space->parked_domain->name);
        }
    }
    return (string) url($url)->domain($space->domain);
}

function fill_data($dateList, $data, $dateKey = 'date', $valueKey = 'value', $alias = null)
{
    if ($data instanceof \think\Collection) {
        $data = $data->toArray();
    }
    $keys = array_column($data, $dateKey);
    array_multisort($keys, SORT_ASC, $data);

    $result  = [];
    $dataKey = 0;

    foreach ($dateList as $date) {
        if (!empty($data[$dataKey]) && Date::parse($data[$dataKey][$dateKey])->equalTo($date)) {
            $result[] = [
                $dateKey            => $date,
                $alias ?? $valueKey => (int) $data[$dataKey][$valueKey],
            ];
            $dataKey++;
        } else {
            $result[] = [
                $dateKey            => $date,
                $alias ?? $valueKey => 0,
            ];
        }
    }

    return $result;
}

function get_date_query($field, $unit = 'hour')
{
    $formats = [
        'minute' => '%Y-%m-%d %H:%i:00',
        'hour'   => '%Y-%m-%d %H:00:00',
        'day'    => '%Y-%m-%d 00:00:00',
        'week'   => '%xW%v',
        'month'  => '%Y-%m-01',
        'year'   => '%Y-01-01',
    ];

    return "date_format({$field}, '{$formats[$unit]}')";
}

function get_period($period = '24hours')
{
    switch ($period) {
        case '1year':
            $start  = Date::now()->subYears(1)->addMonth()->startOfMonth();
            $end    = Date::now()->endOfMonth();
            $unit   = 'month';
            $format = 'Y-m-01';
            break;
        case '6months':
            $start  = Date::now()->subMonths(6)->addWeek()->startOfWeek();
            $end    = Date::now()->endOfWeek();
            $unit   = 'week';
            $format = 'Y-m-d';
            break;
        case '180days':
            $start  = Date::now()->subDays(180)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'week';
            $format = 'Y-m-d';
            break;
        case '90days':
            $start  = Date::now()->subDays(90)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case 'last-month':
            $start  = Date::now()->subMonth()->startOfMonth();
            $end    = Date::now()->subMonth()->endOfMonth();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case '30days':
            $start  = Date::now()->subDays(30)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case '7days':
            $start  = Date::now()->subDays(7)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case '3days':
            $start  = Date::now()->subDays(3)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case 'yesterday':
            $start  = Date::yesterday()->startOfDay();
            $end    = Date::yesterday()->endOfDay();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case '24hours':
        default:
            $start  = Date::now()->subHours(24)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
    }

    $list = [];
    $date = $start->copy();
    do {
        if ($date->gt($end)) {
            break;
        }
        $list[] = $date->format($format);
    } while ($date = $date->add($unit, 1));

    return [$start, $end, $unit, $list];
}
