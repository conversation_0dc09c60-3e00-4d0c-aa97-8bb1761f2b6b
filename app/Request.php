<?php

namespace app;

// 应用请求对象类
use app\model\Domain;
use app\model\Space;
use think\helper\Str;

class Request extends \think\Request
{
    const DOMAIN_APP    = 1; //主域名访问
    const DOMAIN_SUB    = 2; //子域名访问
    const DOMAIN_PARKED = 4; //绑定域名访问

    protected ?Space $space = null;

    protected ?Domain $realDomain = null;

    protected $mode = 1;

    public function setRealDomain(Domain $domain)
    {
        $this->realDomain = $domain;
    }

    public function getRealDomain()
    {
        return $this->realDomain;
    }

    public function setMode($mode)
    {
        $this->mode = $mode;
    }

    public function isMode($mode)
    {
        return ($this->mode & $mode) != 0;
    }

    public function getMode()
    {
        return $this->mode;
    }

    public function setSpace(Space $space)
    {
        $this->space = $space;
    }

    public function getSpace()
    {
        return $this->space;
    }

    public function isJson(): bool
    {
        return Str::startsWith($this->pathinfo(), 'api/') || parent::is<PERSON><PERSON>();
    }
}
