<?php

namespace app\subscribe;

use app\event\BookCreated;
use app\event\BookDeleted;
use app\event\BookEvent;
use app\event\BookReleaseFailed;
use app\event\BookReleaseSucceed;
use app\event\BookRestored;
use app\event\GitPush;
use think\Event;

class Activity
{
    public function onBookEvent(BookEvent $event)
    {
        $type = match (true) {
            $event instanceof BookCreated => \app\model\Activity::TYPE_BOOK_CREATE,
            $event instanceof BookDeleted => \app\model\Activity::TYPE_BOOK_DELETE,
            $event instanceof BookRestored => \app\model\Activity::TYPE_BOOK_RESTORE,
            $event instanceof BookReleaseSucceed => \app\model\Activity::TYPE_BOOK_RELEASE,
            $event instanceof GitPush => \app\model\Activity::TYPE_BOOK_UPDATE,
        };

        $event->book->space->createActivity($event->user, $type, $event->book);
    }

    public function onBookReleaseFailed(BookReleaseFailed $event)
    {
        $event->book->space->createActivity(
            $event->user,
            \app\model\Activity::TYPE_BOOK_RELEASE_FAIL,
            $event->book,
            $event->error
        );
    }

    public function subscribe(Event $event)
    {
        $event->listen(GitPush::class, [$this, 'onBookEvent']);
        $event->listen(BookCreated::class, [$this, 'onBookEvent']);
        $event->listen(BookDeleted::class, [$this, 'onBookEvent']);
        $event->listen(BookRestored::class, [$this, 'onBookEvent']);
        $event->listen(BookReleaseSucceed::class, [$this, 'onBookEvent']);
        $event->listen(BookReleaseFailed::class, [$this, 'onBookReleaseFailed']);
    }
}
