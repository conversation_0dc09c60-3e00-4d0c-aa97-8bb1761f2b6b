<?php

namespace app\subscribe;

use app\event\Register;
use app\model\OrganizationInvite;
use think\Event;
use think\Session;
use yunwuxin\auth\event\Login;

class User
{

    public function onLogin(Session $session, Login $login)
    {
        if ($login->user instanceof \app\model\User) {
            $this->checkInvite($session, $login->user);
        }
    }

    public function onRegister(Session $session, Register $register)
    {
        $this->checkAdmin($register->user);
        $this->checkInvite($session, $register->user);
        $this->checkSpace($register->user);
    }

    /**
     * 检查空间
     * @param \app\model\User $user
     * @return void
     */
    protected function checkSpace(\app\model\User $user)
    {
        if (!is_saas()) {
            //加入默认组织
            $space = get_default_space();
            $space->owner->addMember($user, overwrite: false);
        }
    }

    /**
     * 设置管理员
     * @param \app\model\User $user
     * @return void
     */
    protected function checkAdmin(\app\model\User $user)
    {
        if ($user->id == 1) {
            //ID为1的自动成为管理员
            $user->save([
                'access_level' => 'admin',
            ]);
        }
    }

    /**
     * 检查邀请
     * @param \app\model\User $user
     * @return void
     */
    protected function checkInvite(Session $session, \app\model\User $user)
    {
        $inviteId = $session->pull('invite_id');

        if ($inviteId) {
            $invite = OrganizationInvite::find($inviteId);
            if ($invite && $invite->available) {
                $invite->owner->addMember($user, overwrite: false, invite: $invite);
            }
        }
    }

    public function subscribe(Event $event)
    {
        $event->listen(Register::class, [$this, 'onRegister']);
        $event->listen(Login::class, [$this, 'onLogin']);
    }
}
