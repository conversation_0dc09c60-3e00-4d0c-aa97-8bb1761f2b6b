<?php

namespace app\subscribe;

use app\event\GitPush;
use app\lib\Date;
use think\Event;

class Book
{
    public function onGitPush(GitPush $push)
    {
        try {
            $push->book->isAutoWriteTimestamp(false)->save([
                'update_time' => Date::now(),
            ]);
        } catch (\Exception) {

        }
    }

    public function subscribe(Event $event)
    {
        $event->listen(GitPush::class, [$this, 'onGitPush']);
    }
}
