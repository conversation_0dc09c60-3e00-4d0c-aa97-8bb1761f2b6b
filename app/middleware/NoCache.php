<?php

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;

class NoCache
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        /** @var Response $response */
        $response = $next($request);
        if (!$response->getHeader('Cache-Control')) {
            $response->header([
                'Cache-Control' => 'no-cache',
            ]);
        }

        return $response;
    }
}
